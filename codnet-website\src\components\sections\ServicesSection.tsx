'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import { 
  DevicePhoneMobileIcon, 
  GlobeAltIcon, 
  ComputerDesktopIcon,
  ArrowTopRightOnSquareIcon 
} from '@heroicons/react/24/outline';

const services = [
  {
    id: 'mobile',
    title: 'تطبيقات الهاتف',
    description: 'تطبيقات iOS و Android أصلية ومتقاطعة المنصات بأحدث التقنيات',
    icon: DevicePhoneMobileIcon,
    features: [
      'تطبيقات iOS أصلية (Swift)',
      'تطبيقات Android أصلية (Kotlin)',
      'تطبيقات متقاطعة (React Native, Flutter)',
      'تصميم UI/UX متجاوب',
      'تكامل مع APIs',
      'نشر في المتاجر'
    ],
    technologies: ['React Native', 'Flutter', 'Swift', 'Kotlin', 'Firebase'],
    color: 'from-blue-500 to-cyan-500',
    bgColor: 'bg-blue-50 dark:bg-blue-900/20',
    href: '/services/mobile'
  },
  {
    id: 'web',
    title: 'تطبيقات الويب',
    description: 'مواقع ومنصات ويب حديثة وسريعة ومحسنة لمحركات البحث',
    icon: GlobeAltIcon,
    features: [
      'مواقع ويب متجاوبة',
      'تطبيقات ويب تفاعلية (SPA)',
      'متاجر إلكترونية',
      'أنظمة إدارة المحتوى',
      'تحسين SEO',
      'أمان وحماية عالية'
    ],
    technologies: ['React', 'Next.js', 'Vue.js', 'Node.js', 'Python'],
    color: 'from-green-500 to-emerald-500',
    bgColor: 'bg-green-50 dark:bg-green-900/20',
    href: '/services/web'
  },
  {
    id: 'desktop',
    title: 'تطبيقات الحاسوب',
    description: 'برامج سطح المكتب قوية وموثوقة لأنظمة Windows و macOS و Linux',
    icon: ComputerDesktopIcon,
    features: [
      'تطبيقات Windows أصلية',
      'تطبيقات macOS أصلية',
      'تطبيقات Linux',
      'تطبيقات متقاطعة المنصات',
      'أدوات إنتاجية',
      'أنظمة إدارة البيانات'
    ],
    technologies: ['Electron', 'C#', '.NET', 'Python', 'Java'],
    color: 'from-purple-500 to-pink-500',
    bgColor: 'bg-purple-50 dark:bg-purple-900/20',
    href: '/services/desktop'
  }
];

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2
    }
  }
};

const itemVariants = {
  hidden: { opacity: 0, y: 50 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.6,
      ease: "easeOut"
    }
  }
};

export default function ServicesSection() {
  return (
    <section className="py-20 bg-white dark:bg-slate-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 dark:text-white mb-4">
            خدماتنا <span className="gradient-text">المتخصصة</span>
          </h2>
          <p className="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            نقدم حلولاً تقنية شاملة تغطي جميع احتياجاتك الرقمية، من تطبيقات الهاتف إلى المواقع الإلكترونية وبرامج سطح المكتب
          </p>
        </motion.div>

        {/* Services Grid */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="grid md:grid-cols-2 lg:grid-cols-3 gap-8"
        >
          {services.map((service, index) => (
            <motion.div
              key={service.id}
              variants={itemVariants}
              className="group relative"
            >
              <div className={`relative p-8 rounded-2xl ${service.bgColor} border border-gray-200 dark:border-gray-700 hover:border-transparent card-hover`}>
                {/* Gradient overlay on hover */}
                <div className={`absolute inset-0 bg-gradient-to-br ${service.color} opacity-0 group-hover:opacity-10 rounded-2xl transition-opacity duration-300`} />
                
                {/* Icon */}
                <div className={`inline-flex p-4 rounded-2xl bg-gradient-to-br ${service.color} text-white mb-6 group-hover:scale-110 transition-transform duration-300 shadow-lg pulse-glow`}>
                  <service.icon className="h-8 w-8" />
                </div>

                {/* Content */}
                <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-3">
                  {service.title}
                </h3>
                <p className="text-gray-600 dark:text-gray-300 mb-6 leading-relaxed">
                  {service.description}
                </p>

                {/* Features */}
                <ul className="space-y-2 mb-6">
                  {service.features.slice(0, 4).map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-center text-sm text-gray-600 dark:text-gray-300">
                      <div className={`w-1.5 h-1.5 rounded-full bg-gradient-to-r ${service.color} ml-2 flex-shrink-0`} />
                      {feature}
                    </li>
                  ))}
                </ul>

                {/* Technologies */}
                <div className="flex flex-wrap gap-2 mb-6">
                  {service.technologies.slice(0, 3).map((tech, techIndex) => (
                    <span
                      key={techIndex}
                      className="px-2 py-1 text-xs font-medium bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded-md"
                    >
                      {tech}
                    </span>
                  ))}
                  {service.technologies.length > 3 && (
                    <span className="px-2 py-1 text-xs font-medium bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded-md">
                      +{service.technologies.length - 3}
                    </span>
                  )}
                </div>

                {/* CTA */}
                <Link
                  href={service.href}
                  className="inline-flex items-center text-sm font-semibold text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-300"
                >
                  اعرف المزيد
                  <ArrowTopRightOnSquareIcon className="mr-1 h-4 w-4 group-hover:-translate-y-0.5 group-hover:translate-x-0.5 transition-transform duration-300" />
                </Link>

                {/* Hover effect border */}
                <div className={`absolute inset-0 rounded-2xl bg-gradient-to-br ${service.color} opacity-0 group-hover:opacity-20 transition-opacity duration-300 -z-10 blur-xl`} />
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Bottom CTA */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="text-center mt-16"
        >
          <p className="text-gray-600 dark:text-gray-300 mb-6">
            لديك مشروع مخصص؟ دعنا نساعدك في تحويل فكرتك إلى واقع رقمي
          </p>
          <Link
            href="/contact"
            className="btn-primary inline-flex items-center px-8 py-4 text-white font-bold rounded-xl text-lg"
          >
            ابدأ مشروعك المخصص
            <ArrowTopRightOnSquareIcon className="mr-2 h-5 w-5" />
          </Link>
        </motion.div>
      </div>
    </section>
  );
}
