import { ReactNode } from 'react';
import { motion } from 'framer-motion';

interface CardProps {
  children: ReactNode;
  className?: string;
  hover?: boolean;
  padding?: 'sm' | 'md' | 'lg';
  background?: 'white' | 'gray' | 'gradient';
  border?: boolean;
  shadow?: 'sm' | 'md' | 'lg' | 'xl';
  rounded?: 'sm' | 'md' | 'lg' | 'xl';
  animate?: boolean;
}

export default function Card({
  children,
  className = '',
  hover = true,
  padding = 'md',
  background = 'white',
  border = true,
  shadow = 'md',
  rounded = 'lg',
  animate = true
}: CardProps) {
  const baseClasses = 'transition-all duration-300';
  
  const paddingClasses = {
    sm: 'p-4',
    md: 'p-6',
    lg: 'p-8'
  };

  const backgroundClasses = {
    white: 'bg-white dark:bg-slate-900',
    gray: 'bg-gray-50 dark:bg-slate-800',
    gradient: 'bg-gradient-to-br from-white to-gray-50 dark:from-slate-900 dark:to-slate-800'
  };

  const borderClasses = border 
    ? 'border border-gray-200 dark:border-gray-700' 
    : '';

  const shadowClasses = {
    sm: 'shadow-sm',
    md: 'shadow-md',
    lg: 'shadow-lg',
    xl: 'shadow-xl'
  };

  const roundedClasses = {
    sm: 'rounded-lg',
    md: 'rounded-xl',
    lg: 'rounded-2xl',
    xl: 'rounded-3xl'
  };

  const hoverClasses = hover 
    ? 'card-hover hover:border-transparent' 
    : '';

  const classes = `
    ${baseClasses}
    ${paddingClasses[padding]}
    ${backgroundClasses[background]}
    ${borderClasses}
    ${shadowClasses[shadow]}
    ${roundedClasses[rounded]}
    ${hoverClasses}
    ${className}
  `.trim().replace(/\s+/g, ' ');

  if (animate) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true }}
        transition={{ duration: 0.6 }}
        className={classes}
      >
        {children}
      </motion.div>
    );
  }

  return (
    <div className={classes}>
      {children}
    </div>
  );
}
