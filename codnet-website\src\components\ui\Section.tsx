import { ReactNode } from 'react';
import { motion } from 'framer-motion';

interface SectionProps {
  children: ReactNode;
  className?: string;
  background?: 'white' | 'gray' | 'gradient' | 'dark';
  padding?: 'sm' | 'md' | 'lg' | 'xl';
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '4xl' | '6xl' | '7xl' | 'full';
  id?: string;
  animate?: boolean;
}

export default function Section({
  children,
  className = '',
  background = 'white',
  padding = 'lg',
  maxWidth = '7xl',
  id,
  animate = true
}: SectionProps) {
  const backgroundClasses = {
    white: 'bg-white dark:bg-slate-900',
    gray: 'bg-gray-50 dark:bg-slate-800',
    gradient: 'bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-slate-900 dark:via-blue-900 dark:to-indigo-900',
    dark: 'bg-gray-900 dark:bg-slate-900'
  };

  const paddingClasses = {
    sm: 'py-12',
    md: 'py-16',
    lg: 'py-20',
    xl: 'py-24'
  };

  const maxWidthClasses = {
    sm: 'max-w-sm',
    md: 'max-w-md',
    lg: 'max-w-lg',
    xl: 'max-w-xl',
    '2xl': 'max-w-2xl',
    '4xl': 'max-w-4xl',
    '6xl': 'max-w-6xl',
    '7xl': 'max-w-7xl',
    full: 'max-w-full'
  };

  const sectionClasses = `
    ${backgroundClasses[background]}
    ${paddingClasses[padding]}
    ${className}
  `.trim().replace(/\s+/g, ' ');

  const containerClasses = `
    ${maxWidthClasses[maxWidth]}
    mx-auto
    px-4
    sm:px-6
    lg:px-8
  `.trim().replace(/\s+/g, ' ');

  if (animate) {
    return (
      <section id={id} className={sectionClasses}>
        <motion.div
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
          className={containerClasses}
        >
          {children}
        </motion.div>
      </section>
    );
  }

  return (
    <section id={id} className={sectionClasses}>
      <div className={containerClasses}>
        {children}
      </div>
    </section>
  );
}
