'use client';

import { motion } from 'framer-motion';
import { useState, useEffect } from 'react';
import { ChevronLeftIcon, ChevronRightIcon, StarIcon } from '@heroicons/react/24/solid';

const testimonials = [
  {
    id: 1,
    name: 'أحم<PERSON> محمد',
    position: 'المدير التنفيذي',
    company: 'شركة التجارة الذكية',
    image: '/api/placeholder/80/80',
    rating: 5,
    text: 'فريق Codnet تجاوز توقعاتنا في تطوير تطبيق التجارة الإلكترونية. الجودة العالية والالتزام بالمواعيد جعلنا نحقق نمواً استثنائياً في المبيعات بنسبة 150%.',
    project: 'تطبيق التجارة الإلكترونية',
    results: ['زيادة المبيعات 150%', 'تحسين تجربة المستخدم', '50K+ تحميل']
  },
  {
    id: 2,
    name: 'فاطمة العلي',
    position: 'مديرة التقنية',
    company: 'معهد التقنية المتقدمة',
    image: '/api/placeholder/80/80',
    rating: 5,
    text: 'منصة التعلم الإلكتروني التي طورها فريق Codnet غيرت طريقة تقديم التعليم في معهدنا. التصميم الرائع والوظائف المتقدمة ساعدتنا في الوصول لأكثر من 10 آلاف طالب.',
    project: 'منصة التعلم الإلكتروني',
    results: ['10K+ طالب مسجل', 'تقييم 4.8/5', 'نمو 200%']
  },
  {
    id: 3,
    name: 'خالد السعيد',
    position: 'مالك الشركة',
    company: 'مجموعة الأعمال المتحدة',
    image: '/api/placeholder/80/80',
    rating: 5,
    text: 'نظام إدارة المخزون الذي طوره Codnet وفر علينا 40% من الوقت وحسن دقة العمليات إلى 99.5%. استثمار ممتاز حقق عائداً يفوق 300%.',
    project: 'نظام إدارة المخزون',
    results: ['توفير 40% من الوقت', 'دقة 99.5%', 'ROI 300%']
  },
  {
    id: 4,
    name: 'سارة أحمد',
    position: 'مديرة التسويق',
    company: 'مركز اللياقة الذكي',
    image: '/api/placeholder/80/80',
    rating: 5,
    text: 'تطبيق اللياقة البدنية الذي طوره Codnet حقق نجاحاً باهراً. أكثر من 100 ألف مستخدم نشط وتقييم 4.9 من 5 نجوم. فريق محترف ومبدع.',
    project: 'تطبيق الصحة واللياقة',
    results: ['100K+ مستخدم نشط', 'تقييم 4.9/5', 'نمو 180%']
  },
  {
    id: 5,
    name: 'محمد الراشد',
    position: 'مدير التطوير',
    company: 'الشركة العالمية للاستثمار',
    image: '/api/placeholder/80/80',
    rating: 5,
    text: 'الموقع المؤسسي الذي طوره Codnet رفع مستوى حضورنا الرقمي بشكل كبير. زيادة الزيارات بنسبة 250% وتحسين كبير في محركات البحث.',
    project: 'الموقع المؤسسي',
    results: ['زيادة الزيارات 250%', 'تحسين SEO', 'تقليل معدل الارتداد 60%']
  }
];

export default function TestimonialsSection() {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);

  // Auto-play functionality
  useEffect(() => {
    if (!isAutoPlaying) return;
    
    const interval = setInterval(() => {
      setCurrentIndex((prev) => (prev + 1) % testimonials.length);
    }, 5000);

    return () => clearInterval(interval);
  }, [isAutoPlaying]);

  const nextTestimonial = () => {
    setCurrentIndex((prev) => (prev + 1) % testimonials.length);
    setIsAutoPlaying(false);
  };

  const prevTestimonial = () => {
    setCurrentIndex((prev) => (prev - 1 + testimonials.length) % testimonials.length);
    setIsAutoPlaying(false);
  };

  const goToTestimonial = (index: number) => {
    setCurrentIndex(index);
    setIsAutoPlaying(false);
  };

  return (
    <section className="py-20 bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-slate-900 dark:via-blue-900 dark:to-indigo-900 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-10 w-72 h-72 bg-blue-400/20 rounded-full blur-3xl animate-pulse" />
        <div className="absolute bottom-20 right-10 w-96 h-96 bg-purple-400/20 rounded-full blur-3xl animate-pulse" />
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 dark:text-white mb-4">
            ماذا يقول <span className="gradient-text">عملاؤنا</span>
          </h2>
          <p className="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            شهادات حقيقية من عملائنا الذين حققوا نجاحات استثنائية مع حلولنا التقنية
          </p>
        </motion.div>

        {/* Main Testimonial */}
        <div className="relative max-w-4xl mx-auto">
          <motion.div
            key={currentIndex}
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -50 }}
            transition={{ duration: 0.5 }}
            className="bg-white dark:bg-slate-800 rounded-3xl p-8 md:p-12 shadow-2xl border border-gray-100 dark:border-gray-700"
          >
            {/* Quote Icon */}
            <div className="text-6xl text-blue-600/20 dark:text-blue-400/20 mb-6">
              "
            </div>

            {/* Rating */}
            <div className="flex items-center mb-6">
              {[...Array(testimonials[currentIndex].rating)].map((_, i) => (
                <StarIcon key={i} className="h-5 w-5 text-yellow-400" />
              ))}
            </div>

            {/* Testimonial Text */}
            <blockquote className="text-lg md:text-xl text-gray-700 dark:text-gray-300 leading-relaxed mb-8">
              {testimonials[currentIndex].text}
            </blockquote>

            {/* Results */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
              {testimonials[currentIndex].results.map((result, index) => (
                <div key={index} className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                  <div className="w-2 h-2 rounded-full bg-green-500 ml-2 flex-shrink-0" />
                  {result}
                </div>
              ))}
            </div>

            {/* Client Info */}
            <div className="flex items-center">
              <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold text-xl ml-4">
                {testimonials[currentIndex].name.charAt(0)}
              </div>
              <div>
                <h4 className="text-lg font-semibold text-gray-900 dark:text-white">
                  {testimonials[currentIndex].name}
                </h4>
                <p className="text-gray-600 dark:text-gray-400">
                  {testimonials[currentIndex].position}
                </p>
                <p className="text-blue-600 dark:text-blue-400 font-medium">
                  {testimonials[currentIndex].company}
                </p>
              </div>
            </div>

            {/* Project Badge */}
            <div className="absolute top-8 left-8">
              <span className="px-3 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200 text-sm font-medium rounded-full">
                {testimonials[currentIndex].project}
              </span>
            </div>
          </motion.div>

          {/* Navigation Buttons */}
          <button
            onClick={prevTestimonial}
            className="absolute top-1/2 -translate-y-1/2 -right-6 w-12 h-12 bg-white dark:bg-slate-800 rounded-full shadow-lg flex items-center justify-center text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200"
          >
            <ChevronRightIcon className="h-6 w-6" />
          </button>
          
          <button
            onClick={nextTestimonial}
            className="absolute top-1/2 -translate-y-1/2 -left-6 w-12 h-12 bg-white dark:bg-slate-800 rounded-full shadow-lg flex items-center justify-center text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200"
          >
            <ChevronLeftIcon className="h-6 w-6" />
          </button>
        </div>

        {/* Dots Indicator */}
        <div className="flex justify-center mt-8 space-x-2 rtl:space-x-reverse">
          {testimonials.map((_, index) => (
            <button
              key={index}
              onClick={() => goToTestimonial(index)}
              className={`w-3 h-3 rounded-full transition-all duration-300 ${
                index === currentIndex
                  ? 'bg-blue-600 dark:bg-blue-400 w-8'
                  : 'bg-gray-300 dark:bg-gray-600 hover:bg-gray-400 dark:hover:bg-gray-500'
              }`}
            />
          ))}
        </div>

        {/* Thumbnails */}
        <div className="flex justify-center mt-12 space-x-4 rtl:space-x-reverse overflow-x-auto pb-4">
          {testimonials.map((testimonial, index) => (
            <button
              key={testimonial.id}
              onClick={() => goToTestimonial(index)}
              className={`flex-shrink-0 p-4 rounded-xl transition-all duration-300 ${
                index === currentIndex
                  ? 'bg-blue-100 dark:bg-blue-900/30 border-2 border-blue-600 dark:border-blue-400'
                  : 'bg-white dark:bg-slate-800 border border-gray-200 dark:border-gray-700 hover:border-blue-300 dark:hover:border-blue-600'
              }`}
            >
              <div className="flex items-center space-x-3 rtl:space-x-reverse">
                <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold text-sm">
                  {testimonial.name.charAt(0)}
                </div>
                <div className="text-right">
                  <p className="text-sm font-medium text-gray-900 dark:text-white">
                    {testimonial.name}
                  </p>
                  <p className="text-xs text-gray-600 dark:text-gray-400">
                    {testimonial.company}
                  </p>
                </div>
              </div>
            </button>
          ))}
        </div>
      </div>
    </section>
  );
}
