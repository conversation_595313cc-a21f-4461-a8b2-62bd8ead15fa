'use client';

import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import { motion } from 'framer-motion';
import Link from 'next/link';
import { 
  DevicePhoneMobileIcon, 
  GlobeAltIcon, 
  ComputerDesktopIcon,
  ArrowTopRightOnSquareIcon,
  CheckIcon
} from '@heroicons/react/24/outline';

const services = [
  {
    id: 'mobile',
    title: 'تطبيقات الهاتف',
    description: 'تطبيقات iOS و Android أصلية ومتقاطعة المنصات بأحدث التقنيات',
    icon: DevicePhoneMobileIcon,
    features: [
      'تطبيقات iOS أصلية (Swift)',
      'تطبيقات Android أصلية (Kotlin)',
      'تطبيقات متقاطعة (React Native, Flutter)',
      'تصميم UI/UX متجاوب',
      'تكامل مع APIs',
      'نشر في المتاجر',
      'صيانة ودعم مستمر',
      'تحليلات وإحصائيات'
    ],
    technologies: ['React Native', 'Flutter', 'Swift', 'Kotlin', 'Firebase', 'Redux'],
    color: 'from-blue-500 to-cyan-500',
    bgColor: 'bg-blue-50 dark:bg-blue-900/20',
    href: '/services/mobile',
    price: 'يبدأ من 15,000 ريال'
  },
  {
    id: 'web',
    title: 'تطبيقات الويب',
    description: 'مواقع ومنصات ويب حديثة وسريعة ومحسنة لمحركات البحث',
    icon: GlobeAltIcon,
    features: [
      'مواقع ويب متجاوبة',
      'تطبيقات ويب تفاعلية (SPA)',
      'متاجر إلكترونية',
      'أنظمة إدارة المحتوى',
      'تحسين SEO',
      'أمان وحماية عالية',
      'لوحات تحكم إدارية',
      'تكامل مع خدمات الدفع'
    ],
    technologies: ['React', 'Next.js', 'Vue.js', 'Node.js', 'Python', 'PostgreSQL'],
    color: 'from-green-500 to-emerald-500',
    bgColor: 'bg-green-50 dark:bg-green-900/20',
    href: '/services/web',
    price: 'يبدأ من 8,000 ريال'
  },
  {
    id: 'desktop',
    title: 'تطبيقات الحاسوب',
    description: 'برامج سطح المكتب قوية وموثوقة لأنظمة Windows و macOS و Linux',
    icon: ComputerDesktopIcon,
    features: [
      'تطبيقات Windows أصلية',
      'تطبيقات macOS أصلية',
      'تطبيقات Linux',
      'تطبيقات متقاطعة المنصات',
      'أدوات إنتاجية',
      'أنظمة إدارة البيانات',
      'واجهات مستخدم حديثة',
      'تكامل مع قواعد البيانات'
    ],
    technologies: ['Electron', 'C#', '.NET', 'Python', 'Java', 'Qt'],
    color: 'from-purple-500 to-pink-500',
    bgColor: 'bg-purple-50 dark:bg-purple-900/20',
    href: '/services/desktop',
    price: 'يبدأ من 12,000 ريال'
  }
];

const process = [
  {
    step: '01',
    title: 'التحليل والتخطيط',
    description: 'دراسة شاملة لمتطلباتك وأهدافك مع وضع خطة مفصلة للمشروع'
  },
  {
    step: '02',
    title: 'التصميم والنمذجة',
    description: 'تصميم واجهات مستخدم جذابة وسهلة الاستخدام مع نماذج أولية تفاعلية'
  },
  {
    step: '03',
    title: 'التطوير والبرمجة',
    description: 'تطوير الحل باستخدام أحدث التقنيات مع اختبارات مستمرة'
  },
  {
    step: '04',
    title: 'الاختبار والتسليم',
    description: 'اختبارات شاملة وتسليم المشروع مع التدريب والدعم الفني'
  }
];

export default function ServicesPage() {
  return (
    <div className="page-container">
      <Header />
      <main className="page-content pt-20">
        {/* Hero Section */}
        <section className="section-container py-20 bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-slate-900 dark:via-blue-900 dark:to-indigo-900">
          <div className="content-wrapper text-center">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-6">
                خدماتنا المتخصصة
              </h1>
              <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                نقدم حلولاً تقنية شاملة تغطي جميع احتياجاتك الرقمية، من تطبيقات الهاتف إلى المواقع الإلكترونية وبرامج سطح المكتب
              </p>
            </motion.div>
          </div>
        </section>

        {/* Services Section */}
        <section className="section-container py-20 bg-white dark:bg-slate-900">
          <div className="content-wrapper">
            <div className="grid lg:grid-cols-3 gap-8">
              {services.map((service, index) => (
                <motion.div
                  key={service.id}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: index * 0.2 }}
                  className="group relative"
                >
                  <div className={`relative p-8 rounded-2xl ${service.bgColor} border border-gray-200 dark:border-gray-700 hover:border-transparent card-hover h-full`}>
                    {/* Icon */}
                    <div className={`inline-flex p-4 rounded-2xl bg-gradient-to-br ${service.color} text-white mb-6 group-hover:scale-110 transition-transform duration-300 shadow-lg`}>
                      <service.icon className="h-8 w-8" />
                    </div>

                    {/* Content */}
                    <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-3">
                      {service.title}
                    </h3>
                    <p className="text-gray-600 dark:text-gray-300 mb-6 leading-relaxed">
                      {service.description}
                    </p>

                    {/* Price */}
                    <div className="mb-6">
                      <span className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                        {service.price}
                      </span>
                    </div>

                    {/* Features */}
                    <ul className="space-y-3 mb-8">
                      {service.features.map((feature, featureIndex) => (
                        <li key={featureIndex} className="flex items-center text-sm text-gray-600 dark:text-gray-300">
                          <CheckIcon className="h-4 w-4 text-green-500 ml-2 flex-shrink-0" />
                          {feature}
                        </li>
                      ))}
                    </ul>

                    {/* Technologies */}
                    <div className="mb-8">
                      <h4 className="text-sm font-semibold text-gray-900 dark:text-white mb-3">التقنيات المستخدمة:</h4>
                      <div className="flex flex-wrap gap-2">
                        {service.technologies.map((tech, techIndex) => (
                          <span
                            key={techIndex}
                            className="px-2 py-1 text-xs font-medium bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded-md"
                          >
                            {tech}
                          </span>
                        ))}
                      </div>
                    </div>

                    {/* CTA */}
                    <Link
                      href={service.href}
                      className="btn-primary inline-flex items-center px-6 py-3 text-white font-semibold rounded-xl w-full justify-center"
                    >
                      اعرف المزيد
                      <ArrowTopRightOnSquareIcon className="mr-2 h-5 w-5" />
                    </Link>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Process Section */}
        <section className="section-container py-20 bg-gray-50 dark:bg-slate-800">
          <div className="content-wrapper">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
              className="text-center mb-16"
            >
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
                منهجية العمل المتطورة
              </h2>
              <p className="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                نتبع منهجية عمل مدروسة ومجربة لضمان تسليم مشروعك في الوقت المحدد وبأعلى جودة
              </p>
            </motion.div>
            
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
              {process.map((step, index) => (
                <motion.div
                  key={step.step}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="relative text-center"
                >
                  <div className="text-6xl font-bold text-blue-600/20 dark:text-blue-400/20 mb-4">
                    {step.step}
                  </div>
                  <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                    {step.title}
                  </h4>
                  <p className="text-gray-600 dark:text-gray-300 text-sm leading-relaxed">
                    {step.description}
                  </p>
                  
                  {/* Connector line */}
                  {index < process.length - 1 && (
                    <div className="hidden lg:block absolute top-12 left-full w-full h-0.5 bg-gradient-to-r from-blue-600/50 to-transparent" />
                  )}
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="section-container py-20 bg-blue-600 text-white">
          <div className="content-wrapper text-center">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
            >
              <h2 className="text-3xl md:text-4xl font-bold mb-6">
                هل أنت مستعد لبدء مشروعك؟
              </h2>
              <p className="text-xl mb-8 text-blue-100 max-w-3xl mx-auto">
                تواصل معنا اليوم للحصول على استشارة مجانية ومناقشة كيف يمكننا مساعدتك في تحقيق أهدافك
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link
                  href="/contact"
                  className="btn-secondary inline-flex items-center px-8 py-4 text-white font-bold rounded-xl text-lg"
                >
                  احصل على استشارة مجانية
                </Link>
                <Link
                  href="/portfolio"
                  className="inline-flex items-center px-8 py-4 border-2 border-white text-white hover:bg-white hover:text-blue-600 font-semibold rounded-xl transition-all duration-300"
                >
                  شاهد أعمالنا
                </Link>
              </div>
            </motion.div>
          </div>
        </section>
      </main>
      <Footer />
    </div>
  );
}
