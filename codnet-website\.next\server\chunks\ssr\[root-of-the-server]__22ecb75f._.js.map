{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/My%20Company%20Website/codnet-website/src/components/ui/Logo.tsx"], "sourcesContent": ["import Link from 'next/link';\n\ninterface LogoProps {\n  className?: string;\n  size?: 'sm' | 'md' | 'lg';\n}\n\nexport default function Logo({ className = '', size = 'md' }: LogoProps) {\n  const sizeClasses = {\n    sm: 'h-6 w-6 text-sm',\n    md: 'h-8 w-8 text-lg',\n    lg: 'h-12 w-12 text-2xl'\n  };\n\n  const textSizeClasses = {\n    sm: 'text-lg',\n    md: 'text-xl',\n    lg: 'text-3xl'\n  };\n\n  return (\n    <Link href=\"/\" className={`flex items-center space-x-2 rtl:space-x-reverse ${className}`}>\n      <div className={`${sizeClasses[size]} rounded-xl bg-gradient-to-br from-blue-600 via-blue-700 to-amber-500 flex items-center justify-center shadow-lg pulse-glow`}>\n        <span className={`text-white font-bold ${textSizeClasses[size]}`}>C</span>\n      </div>\n      <span className={`${textSizeClasses[size]} font-bold gradient-text`}>Codnet</span>\n    </Link>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAOe,SAAS,KAAK,EAAE,YAAY,EAAE,EAAE,OAAO,IAAI,EAAa;IACrE,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,kBAAkB;QACtB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC,4JAAA,CAAA,UAAI;QAAC,MAAK;QAAI,WAAW,CAAC,gDAAgD,EAAE,WAAW;;0BACtF,8OAAC;gBAAI,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,2HAA2H,CAAC;0BAC/J,cAAA,8OAAC;oBAAK,WAAW,CAAC,qBAAqB,EAAE,eAAe,CAAC,KAAK,EAAE;8BAAE;;;;;;;;;;;0BAEpE,8OAAC;gBAAK,WAAW,GAAG,eAAe,CAAC,KAAK,CAAC,wBAAwB,CAAC;0BAAE;;;;;;;;;;;;AAG3E", "debugId": null}}, {"offset": {"line": 89, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/My%20Company%20Website/codnet-website/src/components/layout/Header.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport { useTheme } from 'next-themes';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport {\n  Bars3Icon,\n  XMarkIcon,\n  SunIcon,\n  MoonIcon,\n  ChevronDownIcon\n} from '@heroicons/react/24/outline';\nimport Logo from '@/components/ui/Logo';\n\nconst navigation = [\n  { name: 'الرئيسية', href: '/' },\n  { \n    name: 'خدماتنا', \n    href: '/services',\n    submenu: [\n      { name: 'تطبيقات الهاتف', href: '/services/mobile' },\n      { name: 'تطبيقات الويب', href: '/services/web' },\n      { name: 'تطبيقات الحاسوب', href: '/services/desktop' },\n    ]\n  },\n  { name: 'معرض الأعمال', href: '/portfolio' },\n  { name: 'من نحن', href: '/about' },\n  { name: 'المدونة', href: '/blog' },\n  { name: 'الوظائف', href: '/careers' },\n  { name: 'تواصل معنا', href: '/contact' },\n];\n\nexport default function Header() {\n  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);\n  const [scrolled, setScrolled] = useState(false);\n  const { theme, setTheme } = useTheme();\n  const [mounted, setMounted] = useState(false);\n\n  useEffect(() => {\n    setMounted(true);\n    const handleScroll = () => {\n      setScrolled(window.scrollY > 20);\n    };\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  if (!mounted) return null;\n\n  return (\n    <header \n      className={`fixed top-0 w-full z-50 transition-all duration-300 ${\n        scrolled \n          ? 'bg-white/90 dark:bg-slate-900/90 backdrop-blur-md shadow-lg' \n          : 'bg-transparent'\n      }`}\n    >\n      <nav className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex h-16 items-center justify-between\">\n          {/* Logo */}\n          <div className=\"flex items-center\">\n            <Logo />\n          </div>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:block\">\n            <div className=\"flex items-center space-x-8 rtl:space-x-reverse\">\n              {navigation.map((item) => (\n                <div key={item.name} className=\"relative group\">\n                  <Link\n                    href={item.href}\n                    className=\"text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 px-3 py-2 text-sm font-medium transition-colors duration-200 flex items-center\"\n                  >\n                    {item.name}\n                    {item.submenu && (\n                      <ChevronDownIcon className=\"mr-1 h-4 w-4 transition-transform group-hover:rotate-180\" />\n                    )}\n                  </Link>\n                  \n                  {/* Submenu */}\n                  {item.submenu && (\n                    <div className=\"absolute right-0 mt-2 w-48 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 bg-white dark:bg-slate-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700\">\n                      <div className=\"py-2\">\n                        {item.submenu.map((subItem) => (\n                          <Link\n                            key={subItem.name}\n                            href={subItem.href}\n                            className=\"block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-slate-700 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200\"\n                          >\n                            {subItem.name}\n                          </Link>\n                        ))}\n                      </div>\n                    </div>\n                  )}\n                </div>\n              ))}\n            </div>\n          </div>\n\n          {/* Right side buttons */}\n          <div className=\"flex items-center space-x-4 rtl:space-x-reverse\">\n            {/* Theme toggle */}\n            <button\n              onClick={() => setTheme(theme === 'dark' ? 'light' : 'dark')}\n              className=\"p-2 rounded-lg bg-gray-100 dark:bg-slate-800 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-slate-700 transition-colors duration-200\"\n            >\n              {theme === 'dark' ? (\n                <SunIcon className=\"h-5 w-5\" />\n              ) : (\n                <MoonIcon className=\"h-5 w-5\" />\n              )}\n            </button>\n\n            {/* CTA Button */}\n            <Link\n              href=\"/contact\"\n              className=\"hidden md:inline-flex btn-primary items-center px-6 py-3 text-white font-semibold rounded-lg text-sm\"\n            >\n              ابدأ مشروعك\n            </Link>\n\n            {/* Mobile menu button */}\n            <button\n              type=\"button\"\n              className=\"md:hidden p-2 rounded-lg bg-gray-100 dark:bg-slate-800 text-gray-700 dark:text-gray-300\"\n              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}\n            >\n              {mobileMenuOpen ? (\n                <XMarkIcon className=\"h-6 w-6\" />\n              ) : (\n                <Bars3Icon className=\"h-6 w-6\" />\n              )}\n            </button>\n          </div>\n        </div>\n      </nav>\n\n      {/* Mobile menu */}\n      <AnimatePresence>\n        {mobileMenuOpen && (\n          <motion.div\n            initial={{ opacity: 0, height: 0 }}\n            animate={{ opacity: 1, height: 'auto' }}\n            exit={{ opacity: 0, height: 0 }}\n            className=\"md:hidden bg-white dark:bg-slate-900 border-t border-gray-200 dark:border-gray-700\"\n          >\n            <div className=\"px-4 py-6 space-y-4\">\n              {navigation.map((item) => (\n                <div key={item.name}>\n                  <Link\n                    href={item.href}\n                    className=\"block text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 py-2 text-base font-medium transition-colors duration-200\"\n                    onClick={() => setMobileMenuOpen(false)}\n                  >\n                    {item.name}\n                  </Link>\n                  {item.submenu && (\n                    <div className=\"mr-4 mt-2 space-y-2\">\n                      {item.submenu.map((subItem) => (\n                        <Link\n                          key={subItem.name}\n                          href={subItem.href}\n                          className=\"block text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 py-1 text-sm transition-colors duration-200\"\n                          onClick={() => setMobileMenuOpen(false)}\n                        >\n                          {subItem.name}\n                        </Link>\n                      ))}\n                    </div>\n                  )}\n                </div>\n              ))}\n              <Link\n                href=\"/contact\"\n                className=\"btn-primary block w-full text-center px-4 py-3 text-white font-semibold rounded-lg\"\n                onClick={() => setMobileMenuOpen(false)}\n              >\n                ابدأ مشروعك\n              </Link>\n            </div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAOA;AAbA;;;;;;;;AAeA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAY,MAAM;IAAI;IAC9B;QACE,MAAM;QACN,MAAM;QACN,SAAS;YACP;gBAAE,MAAM;gBAAkB,MAAM;YAAmB;YACnD;gBAAE,MAAM;gBAAiB,MAAM;YAAgB;YAC/C;gBAAE,MAAM;gBAAmB,MAAM;YAAoB;SACtD;IACH;IACA;QAAE,MAAM;QAAgB,MAAM;IAAa;IAC3C;QAAE,MAAM;QAAU,MAAM;IAAS;IACjC;QAAE,MAAM;QAAW,MAAM;IAAQ;IACjC;QAAE,MAAM;QAAW,MAAM;IAAW;IACpC;QAAE,MAAM;QAAc,MAAM;IAAW;CACxC;AAEc,SAAS;IACtB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;QACX,MAAM,eAAe;YACnB,YAAY,OAAO,OAAO,GAAG;QAC/B;QACA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,IAAI,CAAC,SAAS,OAAO;IAErB,qBACE,8OAAC;QACC,WAAW,CAAC,oDAAoD,EAC9D,WACI,gEACA,kBACJ;;0BAEF,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,gIAAA,CAAA,UAAI;;;;;;;;;;sCAIP,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC;wCAAoB,WAAU;;0DAC7B,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;;oDAET,KAAK,IAAI;oDACT,KAAK,OAAO,kBACX,8OAAC,6NAAA,CAAA,kBAAe;wDAAC,WAAU;;;;;;;;;;;;4CAK9B,KAAK,OAAO,kBACX,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;8DACZ,KAAK,OAAO,CAAC,GAAG,CAAC,CAAC,wBACjB,8OAAC,4JAAA,CAAA,UAAI;4DAEH,MAAM,QAAQ,IAAI;4DAClB,WAAU;sEAET,QAAQ,IAAI;2DAJR,QAAQ,IAAI;;;;;;;;;;;;;;;;uCAjBnB,KAAK,IAAI;;;;;;;;;;;;;;;sCAiCzB,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCACC,SAAS,IAAM,SAAS,UAAU,SAAS,UAAU;oCACrD,WAAU;8CAET,UAAU,uBACT,8OAAC,6MAAA,CAAA,UAAO;wCAAC,WAAU;;;;;6DAEnB,8OAAC,+MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;8CAKxB,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAKD,8OAAC;oCACC,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,kBAAkB,CAAC;8CAEjC,+BACC,8OAAC,iNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;6DAErB,8OAAC,iNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ/B,8OAAC,yLAAA,CAAA,kBAAe;0BACb,gCACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBACjC,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAO;oBACtC,MAAM;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBAC9B,WAAU;8BAEV,cAAA,8OAAC;wBAAI,WAAU;;4BACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC;;sDACC,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAM,KAAK,IAAI;4CACf,WAAU;4CACV,SAAS,IAAM,kBAAkB;sDAEhC,KAAK,IAAI;;;;;;wCAEX,KAAK,OAAO,kBACX,8OAAC;4CAAI,WAAU;sDACZ,KAAK,OAAO,CAAC,GAAG,CAAC,CAAC,wBACjB,8OAAC,4JAAA,CAAA,UAAI;oDAEH,MAAM,QAAQ,IAAI;oDAClB,WAAU;oDACV,SAAS,IAAM,kBAAkB;8DAEhC,QAAQ,IAAI;mDALR,QAAQ,IAAI;;;;;;;;;;;mCAZjB,KAAK,IAAI;;;;;0CAwBrB,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,kBAAkB;0CAClC;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf", "debugId": null}}, {"offset": {"line": 418, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/My%20Company%20Website/codnet-website/src/components/sections/HeroSection.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport Link from 'next/link';\nimport { ArrowLeftIcon, PlayIcon } from '@heroicons/react/24/outline';\n\nconst stats = [\n  { number: '100+', label: 'مشروع مكتمل' },\n  { number: '50+', label: 'عميل راضي' },\n  { number: '5+', label: 'سنوات خبرة' },\n  { number: '24/7', label: 'دعم فني' },\n];\n\nconst technologies = [\n  'React', 'Next.js', 'React Native', 'Flutter', 'Node.js', 'Python', 'Swift', 'Kotlin'\n];\n\nexport default function HeroSection() {\n  // Video modal state for future implementation\n  // const [isVideoPlaying, setIsVideoPlaying] = useState(false);\n\n  return (\n    <section className=\"relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 dark:from-slate-900 dark:via-blue-900 dark:to-indigo-900\">\n      {/* Background Elements */}\n      <div className=\"absolute inset-0\">\n        {/* Animated background shapes */}\n        <motion.div\n          className=\"absolute top-20 left-10 w-72 h-72 bg-blue-400/20 rounded-full blur-3xl\"\n          animate={{\n            x: [0, 100, 0],\n            y: [0, -50, 0],\n          }}\n          transition={{\n            duration: 20,\n            repeat: Infinity,\n            ease: \"linear\"\n          }}\n        />\n        <motion.div\n          className=\"absolute bottom-20 right-10 w-96 h-96 bg-amber-400/20 rounded-full blur-3xl\"\n          animate={{\n            x: [0, -80, 0],\n            y: [0, 60, 0],\n          }}\n          transition={{\n            duration: 25,\n            repeat: Infinity,\n            ease: \"linear\"\n          }}\n        />\n        \n        {/* Grid pattern */}\n        <div className=\"absolute inset-0 opacity-40\">\n          <div className=\"absolute inset-0 bg-gradient-to-r from-blue-500/5 to-purple-500/5\" />\n        </div>\n      </div>\n\n      <div className=\"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-20\">\n        <div className=\"grid lg:grid-cols-2 gap-12 items-center\">\n          {/* Left Content */}\n          <motion.div\n            initial={{ opacity: 0, x: -50 }}\n            animate={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8 }}\n            className=\"text-center lg:text-right\"\n          >\n            {/* Badge */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.2 }}\n              className=\"inline-flex items-center px-4 py-2 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200 text-sm font-medium mb-6\"\n            >\n              <span className=\"w-2 h-2 bg-blue-500 rounded-full ml-2 animate-pulse\"></span>\n              شركة رائدة في تطوير البرمجيات\n            </motion.div>\n\n            {/* Main Heading */}\n            <motion.h1\n              initial={{ opacity: 0, y: 30 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.3 }}\n              className=\"text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 dark:text-white mb-6 leading-tight\"\n            >\n              نصمم{' '}\n              <span className=\"gradient-text\">المستقبل الرقمي</span>\n              {' '}لعملك\n            </motion.h1>\n\n            {/* Subtitle */}\n            <motion.p\n              initial={{ opacity: 0, y: 30 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.4 }}\n              className=\"text-lg md:text-xl text-gray-600 dark:text-gray-300 mb-8 leading-relaxed max-w-2xl mx-auto lg:mx-0\"\n            >\n              نحول أفكارك إلى حلول رقمية مبتكرة. تطبيقات الهاتف، الويب، والحاسوب بأعلى معايير الجودة والأداء.\n            </motion.p>\n\n            {/* CTA Buttons */}\n            <motion.div\n              initial={{ opacity: 0, y: 30 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.5 }}\n              className=\"flex flex-col sm:flex-row gap-4 justify-center lg:justify-start mb-12\"\n            >\n              <Link\n                href=\"/contact\"\n                className=\"group btn-primary inline-flex items-center px-8 py-4 text-white font-bold rounded-xl text-lg\"\n              >\n                ابدأ مشروعك الآن\n                <ArrowLeftIcon className=\"mr-2 h-5 w-5 group-hover:-translate-x-1 transition-transform duration-300\" />\n              </Link>\n\n              <Link\n                href=\"/portfolio\"\n                className=\"group inline-flex items-center px-8 py-4 glass-effect text-gray-900 dark:text-white font-semibold rounded-xl border border-gray-200/30 dark:border-gray-700/30 hover:bg-white/20 dark:hover:bg-slate-800/40 transition-all duration-300 transform hover:-translate-y-1\"\n              >\n                <PlayIcon className=\"ml-2 h-5 w-5 group-hover:scale-110 transition-transform duration-300\" />\n                شاهد أعمالنا\n              </Link>\n            </motion.div>\n\n            {/* Stats */}\n            <motion.div\n              initial={{ opacity: 0, y: 30 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.6 }}\n              className=\"grid grid-cols-2 md:grid-cols-4 gap-6\"\n            >\n              {stats.map((stat, index) => (\n                <motion.div\n                  key={stat.label}\n                  initial={{ opacity: 0, scale: 0.8 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  transition={{ delay: 0.7 + index * 0.1 }}\n                  className=\"text-center\"\n                >\n                  <div className=\"text-2xl md:text-3xl font-bold text-blue-600 dark:text-blue-400 mb-1\">\n                    {stat.number}\n                  </div>\n                  <div className=\"text-sm text-gray-600 dark:text-gray-400\">\n                    {stat.label}\n                  </div>\n                </motion.div>\n              ))}\n            </motion.div>\n          </motion.div>\n\n          {/* Right Content - Visual Elements */}\n          <motion.div\n            initial={{ opacity: 0, x: 50 }}\n            animate={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8, delay: 0.2 }}\n            className=\"relative\"\n          >\n            {/* Main Visual */}\n            <div className=\"relative\">\n              {/* Phone mockup */}\n              <motion.div\n                initial={{ opacity: 0, y: 50 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: 0.8 }}\n                className=\"relative z-10 mx-auto w-64 h-96 bg-gradient-to-br from-gray-900 to-gray-800 rounded-[2.5rem] p-2 shadow-2xl\"\n              >\n                <div className=\"w-full h-full bg-gradient-to-br from-blue-500 to-purple-600 rounded-[2rem] p-4 flex flex-col\">\n                  <div className=\"flex items-center justify-between mb-4\">\n                    <div className=\"w-12 h-12 bg-white/20 rounded-xl\"></div>\n                    <div className=\"flex space-x-1\">\n                      <div className=\"w-2 h-2 bg-white/60 rounded-full\"></div>\n                      <div className=\"w-2 h-2 bg-white/60 rounded-full\"></div>\n                      <div className=\"w-2 h-2 bg-white rounded-full\"></div>\n                    </div>\n                  </div>\n                  <div className=\"flex-1 space-y-3\">\n                    <div className=\"h-4 bg-white/30 rounded\"></div>\n                    <div className=\"h-4 bg-white/20 rounded w-3/4\"></div>\n                    <div className=\"h-20 bg-white/10 rounded-lg\"></div>\n                    <div className=\"grid grid-cols-2 gap-2\">\n                      <div className=\"h-12 bg-white/20 rounded\"></div>\n                      <div className=\"h-12 bg-white/20 rounded\"></div>\n                    </div>\n                  </div>\n                </div>\n              </motion.div>\n\n              {/* Floating elements */}\n              <motion.div\n                animate={{ y: [-10, 10, -10] }}\n                transition={{ duration: 3, repeat: Infinity, ease: \"easeInOut\" }}\n                className=\"absolute -top-4 -left-4 w-16 h-16 bg-amber-400 rounded-xl shadow-lg flex items-center justify-center\"\n              >\n                <span className=\"text-white font-bold text-xl\">⚡</span>\n              </motion.div>\n\n              <motion.div\n                animate={{ y: [10, -10, 10] }}\n                transition={{ duration: 4, repeat: Infinity, ease: \"easeInOut\" }}\n                className=\"absolute -bottom-4 -right-4 w-20 h-20 bg-green-400 rounded-xl shadow-lg flex items-center justify-center\"\n              >\n                <span className=\"text-white font-bold text-2xl\">✓</span>\n              </motion.div>\n            </div>\n\n            {/* Technology badges */}\n            <motion.div\n              initial={{ opacity: 0 }}\n              animate={{ opacity: 1 }}\n              transition={{ delay: 1.2 }}\n              className=\"absolute -bottom-8 left-1/2 transform -translate-x-1/2 flex flex-wrap justify-center gap-2 max-w-sm\"\n            >\n              {technologies.map((tech, index) => (\n                <motion.span\n                  key={tech}\n                  initial={{ opacity: 0, scale: 0 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  transition={{ delay: 1.3 + index * 0.1 }}\n                  className=\"px-3 py-1 bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm text-xs font-medium text-gray-700 dark:text-gray-300 rounded-full border border-gray-200 dark:border-gray-700\"\n                >\n                  {tech}\n                </motion.span>\n              ))}\n            </motion.div>\n          </motion.div>\n        </div>\n      </div>\n\n      {/* Scroll indicator */}\n      <motion.div\n        initial={{ opacity: 0 }}\n        animate={{ opacity: 1 }}\n        transition={{ delay: 2 }}\n        className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2\"\n      >\n        <motion.div\n          animate={{ y: [0, 10, 0] }}\n          transition={{ duration: 2, repeat: Infinity }}\n          className=\"w-6 h-10 border-2 border-gray-400 dark:border-gray-600 rounded-full flex justify-center\"\n        >\n          <motion.div\n            animate={{ y: [0, 12, 0] }}\n            transition={{ duration: 2, repeat: Infinity }}\n            className=\"w-1 h-3 bg-gray-400 dark:bg-gray-600 rounded-full mt-2\"\n          />\n        </motion.div>\n      </motion.div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAJA;;;;;AAMA,MAAM,QAAQ;IACZ;QAAE,QAAQ;QAAQ,OAAO;IAAc;IACvC;QAAE,QAAQ;QAAO,OAAO;IAAY;IACpC;QAAE,QAAQ;QAAM,OAAO;IAAa;IACpC;QAAE,QAAQ;QAAQ,OAAO;IAAU;CACpC;AAED,MAAM,eAAe;IACnB;IAAS;IAAW;IAAgB;IAAW;IAAW;IAAU;IAAS;CAC9E;AAEc,SAAS;IACtB,8CAA8C;IAC9C,+DAA+D;IAE/D,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BACP,GAAG;gCAAC;gCAAG;gCAAK;6BAAE;4BACd,GAAG;gCAAC;gCAAG,CAAC;gCAAI;6BAAE;wBAChB;wBACA,YAAY;4BACV,UAAU;4BACV,QAAQ;4BACR,MAAM;wBACR;;;;;;kCAEF,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BACP,GAAG;gCAAC;gCAAG,CAAC;gCAAI;6BAAE;4BACd,GAAG;gCAAC;gCAAG;gCAAI;6BAAE;wBACf;wBACA,YAAY;4BACV,UAAU;4BACV,QAAQ;4BACR,MAAM;wBACR;;;;;;kCAIF,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;;;;;;;;;;;;;;;;0BAInB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC9B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,WAAU;;8CAGV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,OAAO;oCAAI;oCACzB,WAAU;;sDAEV,8OAAC;4CAAK,WAAU;;;;;;wCAA6D;;;;;;;8CAK/E,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;oCACR,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,OAAO;oCAAI;oCACzB,WAAU;;wCACX;wCACM;sDACL,8OAAC;4CAAK,WAAU;sDAAgB;;;;;;wCAC/B;wCAAI;;;;;;;8CAIP,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;oCACP,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,OAAO;oCAAI;oCACzB,WAAU;8CACX;;;;;;8CAKD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,OAAO;oCAAI;oCACzB,WAAU;;sDAEV,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;;gDACX;8DAEC,8OAAC,yNAAA,CAAA,gBAAa;oDAAC,WAAU;;;;;;;;;;;;sDAG3B,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;;8DAEV,8OAAC,+MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAAyE;;;;;;;;;;;;;8CAMjG,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,OAAO;oCAAI;oCACzB,WAAU;8CAET,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CAET,SAAS;gDAAE,SAAS;gDAAG,OAAO;4CAAI;4CAClC,SAAS;gDAAE,SAAS;gDAAG,OAAO;4CAAE;4CAChC,YAAY;gDAAE,OAAO,MAAM,QAAQ;4CAAI;4CACvC,WAAU;;8DAEV,8OAAC;oDAAI,WAAU;8DACZ,KAAK,MAAM;;;;;;8DAEd,8OAAC;oDAAI,WAAU;8DACZ,KAAK,KAAK;;;;;;;2CAVR,KAAK,KAAK;;;;;;;;;;;;;;;;sCAkBvB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;;8CAGV,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAC5B,YAAY;gDAAE,OAAO;4CAAI;4CACzB,WAAU;sDAEV,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;;;;;;kFACf,8OAAC;wEAAI,WAAU;;;;;;kFACf,8OAAC;wEAAI,WAAU;;;;;;;;;;;;;;;;;;kEAGnB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;;;;;;kFACf,8OAAC;wEAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAOvB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,SAAS;gDAAE,GAAG;oDAAC,CAAC;oDAAI;oDAAI,CAAC;iDAAG;4CAAC;4CAC7B,YAAY;gDAAE,UAAU;gDAAG,QAAQ;gDAAU,MAAM;4CAAY;4CAC/D,WAAU;sDAEV,cAAA,8OAAC;gDAAK,WAAU;0DAA+B;;;;;;;;;;;sDAGjD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,SAAS;gDAAE,GAAG;oDAAC;oDAAI,CAAC;oDAAI;iDAAG;4CAAC;4CAC5B,YAAY;gDAAE,UAAU;gDAAG,QAAQ;gDAAU,MAAM;4CAAY;4CAC/D,WAAU;sDAEV,cAAA,8OAAC;gDAAK,WAAU;0DAAgC;;;;;;;;;;;;;;;;;8CAKpD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;oCAAE;oCACtB,SAAS;wCAAE,SAAS;oCAAE;oCACtB,YAAY;wCAAE,OAAO;oCAAI;oCACzB,WAAU;8CAET,aAAa,GAAG,CAAC,CAAC,MAAM,sBACvB,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;4CAEV,SAAS;gDAAE,SAAS;gDAAG,OAAO;4CAAE;4CAChC,SAAS;gDAAE,SAAS;gDAAG,OAAO;4CAAE;4CAChC,YAAY;gDAAE,OAAO,MAAM,QAAQ;4CAAI;4CACvC,WAAU;sDAET;2CANI;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAejB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,YAAY;oBAAE,OAAO;gBAAE;gBACvB,WAAU;0BAEV,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,GAAG;4BAAC;4BAAG;4BAAI;yBAAE;oBAAC;oBACzB,YAAY;wBAAE,UAAU;wBAAG,QAAQ;oBAAS;oBAC5C,WAAU;8BAEV,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,GAAG;gCAAC;gCAAG;gCAAI;6BAAE;wBAAC;wBACzB,YAAY;4BAAE,UAAU;4BAAG,QAAQ;wBAAS;wBAC5C,WAAU;;;;;;;;;;;;;;;;;;;;;;AAMtB", "debugId": null}}, {"offset": {"line": 1071, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/My%20Company%20Website/codnet-website/src/components/sections/ServicesSection.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport Link from 'next/link';\nimport { \n  DevicePhoneMobileIcon, \n  GlobeAltIcon, \n  ComputerDesktopIcon,\n  ArrowTopRightOnSquareIcon \n} from '@heroicons/react/24/outline';\n\nconst services = [\n  {\n    id: 'mobile',\n    title: 'تطبيقات الهاتف',\n    description: 'تطبيقات iOS و Android أصلية ومتقاطعة المنصات بأحدث التقنيات',\n    icon: DevicePhoneMobileIcon,\n    features: [\n      'تطبيقات iOS أصلية (Swift)',\n      'تطبيقات Android أصلية (Kotlin)',\n      'تطبيقات متقاطعة (React Native, Flutter)',\n      'تصميم UI/UX متجاوب',\n      'تكامل مع APIs',\n      'نشر في المتاجر'\n    ],\n    technologies: ['React Native', 'Flutter', 'Swift', 'Kotlin', 'Firebase'],\n    color: 'from-blue-500 to-cyan-500',\n    bgColor: 'bg-blue-50 dark:bg-blue-900/20',\n    href: '/services/mobile'\n  },\n  {\n    id: 'web',\n    title: 'تطبيقات الويب',\n    description: 'مواقع ومنصات ويب حديثة وسريعة ومحسنة لمحركات البحث',\n    icon: GlobeAltIcon,\n    features: [\n      'مواقع ويب متجاوبة',\n      'تطبيقات ويب تفاعلية (SPA)',\n      'متاجر إلكترونية',\n      'أنظمة إدارة المحتوى',\n      'تحسين SEO',\n      'أمان وحماية عالية'\n    ],\n    technologies: ['React', 'Next.js', 'Vue.js', 'Node.js', 'Python'],\n    color: 'from-green-500 to-emerald-500',\n    bgColor: 'bg-green-50 dark:bg-green-900/20',\n    href: '/services/web'\n  },\n  {\n    id: 'desktop',\n    title: 'تطبيقات الحاسوب',\n    description: 'برامج سطح المكتب قوية وموثوقة لأنظمة Windows و macOS و Linux',\n    icon: ComputerDesktopIcon,\n    features: [\n      'تطبيقات Windows أصلية',\n      'تطبيقات macOS أصلية',\n      'تطبيقات Linux',\n      'تطبيقات متقاطعة المنصات',\n      'أدوات إنتاجية',\n      'أنظمة إدارة البيانات'\n    ],\n    technologies: ['Electron', 'C#', '.NET', 'Python', 'Java'],\n    color: 'from-purple-500 to-pink-500',\n    bgColor: 'bg-purple-50 dark:bg-purple-900/20',\n    href: '/services/desktop'\n  }\n];\n\nconst containerVariants = {\n  hidden: { opacity: 0 },\n  visible: {\n    opacity: 1,\n    transition: {\n      staggerChildren: 0.2\n    }\n  }\n};\n\nconst itemVariants = {\n  hidden: { opacity: 0, y: 50 },\n  visible: {\n    opacity: 1,\n    y: 0,\n    transition: {\n      duration: 0.6,\n      ease: \"easeOut\"\n    }\n  }\n};\n\nexport default function ServicesSection() {\n  return (\n    <section className=\"py-20 bg-white dark:bg-slate-900\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Section Header */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          viewport={{ once: true }}\n          transition={{ duration: 0.6 }}\n          className=\"text-center mb-16\"\n        >\n          <h2 className=\"text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 dark:text-white mb-4\">\n            خدماتنا <span className=\"gradient-text\">المتخصصة</span>\n          </h2>\n          <p className=\"text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto\">\n            نقدم حلولاً تقنية شاملة تغطي جميع احتياجاتك الرقمية، من تطبيقات الهاتف إلى المواقع الإلكترونية وبرامج سطح المكتب\n          </p>\n        </motion.div>\n\n        {/* Services Grid */}\n        <motion.div\n          variants={containerVariants}\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={{ once: true }}\n          className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-8\"\n        >\n          {services.map((service, index) => (\n            <motion.div\n              key={service.id}\n              variants={itemVariants}\n              className=\"group relative\"\n            >\n              <div className={`relative p-8 rounded-2xl ${service.bgColor} border border-gray-200 dark:border-gray-700 hover:border-transparent card-hover`}>\n                {/* Gradient overlay on hover */}\n                <div className={`absolute inset-0 bg-gradient-to-br ${service.color} opacity-0 group-hover:opacity-10 rounded-2xl transition-opacity duration-300`} />\n                \n                {/* Icon */}\n                <div className={`inline-flex p-4 rounded-2xl bg-gradient-to-br ${service.color} text-white mb-6 group-hover:scale-110 transition-transform duration-300 shadow-lg pulse-glow`}>\n                  <service.icon className=\"h-8 w-8\" />\n                </div>\n\n                {/* Content */}\n                <h3 className=\"text-xl font-bold text-gray-900 dark:text-white mb-3\">\n                  {service.title}\n                </h3>\n                <p className=\"text-gray-600 dark:text-gray-300 mb-6 leading-relaxed\">\n                  {service.description}\n                </p>\n\n                {/* Features */}\n                <ul className=\"space-y-2 mb-6\">\n                  {service.features.slice(0, 4).map((feature, featureIndex) => (\n                    <li key={featureIndex} className=\"flex items-center text-sm text-gray-600 dark:text-gray-300\">\n                      <div className={`w-1.5 h-1.5 rounded-full bg-gradient-to-r ${service.color} ml-2 flex-shrink-0`} />\n                      {feature}\n                    </li>\n                  ))}\n                </ul>\n\n                {/* Technologies */}\n                <div className=\"flex flex-wrap gap-2 mb-6\">\n                  {service.technologies.slice(0, 3).map((tech, techIndex) => (\n                    <span\n                      key={techIndex}\n                      className=\"px-2 py-1 text-xs font-medium bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded-md\"\n                    >\n                      {tech}\n                    </span>\n                  ))}\n                  {service.technologies.length > 3 && (\n                    <span className=\"px-2 py-1 text-xs font-medium bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded-md\">\n                      +{service.technologies.length - 3}\n                    </span>\n                  )}\n                </div>\n\n                {/* CTA */}\n                <Link\n                  href={service.href}\n                  className=\"inline-flex items-center text-sm font-semibold text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-300\"\n                >\n                  اعرف المزيد\n                  <ArrowTopRightOnSquareIcon className=\"mr-1 h-4 w-4 group-hover:-translate-y-0.5 group-hover:translate-x-0.5 transition-transform duration-300\" />\n                </Link>\n\n                {/* Hover effect border */}\n                <div className={`absolute inset-0 rounded-2xl bg-gradient-to-br ${service.color} opacity-0 group-hover:opacity-20 transition-opacity duration-300 -z-10 blur-xl`} />\n              </div>\n            </motion.div>\n          ))}\n        </motion.div>\n\n        {/* Bottom CTA */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          viewport={{ once: true }}\n          transition={{ duration: 0.6, delay: 0.4 }}\n          className=\"text-center mt-16\"\n        >\n          <p className=\"text-gray-600 dark:text-gray-300 mb-6\">\n            لديك مشروع مخصص؟ دعنا نساعدك في تحويل فكرتك إلى واقع رقمي\n          </p>\n          <Link\n            href=\"/contact\"\n            className=\"btn-primary inline-flex items-center px-8 py-4 text-white font-bold rounded-xl text-lg\"\n          >\n            ابدأ مشروعك المخصص\n            <ArrowTopRightOnSquareIcon className=\"mr-2 h-5 w-5\" />\n          </Link>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAJA;;;;;AAWA,MAAM,WAAW;IACf;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM,yOAAA,CAAA,wBAAqB;QAC3B,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;SACD;QACD,cAAc;YAAC;YAAgB;YAAW;YAAS;YAAU;SAAW;QACxE,OAAO;QACP,SAAS;QACT,MAAM;IACR;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM,uNAAA,CAAA,eAAY;QAClB,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;SACD;QACD,cAAc;YAAC;YAAS;YAAW;YAAU;YAAW;SAAS;QACjE,OAAO;QACP,SAAS;QACT,MAAM;IACR;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM,qOAAA,CAAA,sBAAmB;QACzB,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;SACD;QACD,cAAc;YAAC;YAAY;YAAM;YAAQ;YAAU;SAAO;QAC1D,OAAO;QACP,SAAS;QACT,MAAM;IACR;CACD;AAED,MAAM,oBAAoB;IACxB,QAAQ;QAAE,SAAS;IAAE;IACrB,SAAS;QACP,SAAS;QACT,YAAY;YACV,iBAAiB;QACnB;IACF;AACF;AAEA,MAAM,eAAe;IACnB,QAAQ;QAAE,SAAS;QAAG,GAAG;IAAG;IAC5B,SAAS;QACP,SAAS;QACT,GAAG;QACH,YAAY;YACV,UAAU;YACV,MAAM;QACR;IACF;AACF;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;;sCAEV,8OAAC;4BAAG,WAAU;;gCAAgF;8CACpF,8OAAC;oCAAK,WAAU;8CAAgB;;;;;;;;;;;;sCAE1C,8OAAC;4BAAE,WAAU;sCAA6D;;;;;;;;;;;;8BAM5E,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,UAAU;oBACV,SAAQ;oBACR,aAAY;oBACZ,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;8BAET,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,UAAU;4BACV,WAAU;sCAEV,cAAA,8OAAC;gCAAI,WAAW,CAAC,yBAAyB,EAAE,QAAQ,OAAO,CAAC,gFAAgF,CAAC;;kDAE3I,8OAAC;wCAAI,WAAW,CAAC,mCAAmC,EAAE,QAAQ,KAAK,CAAC,6EAA6E,CAAC;;;;;;kDAGlJ,8OAAC;wCAAI,WAAW,CAAC,8CAA8C,EAAE,QAAQ,KAAK,CAAC,6FAA6F,CAAC;kDAC3K,cAAA,8OAAC,QAAQ,IAAI;4CAAC,WAAU;;;;;;;;;;;kDAI1B,8OAAC;wCAAG,WAAU;kDACX,QAAQ,KAAK;;;;;;kDAEhB,8OAAC;wCAAE,WAAU;kDACV,QAAQ,WAAW;;;;;;kDAItB,8OAAC;wCAAG,WAAU;kDACX,QAAQ,QAAQ,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,SAAS,6BAC1C,8OAAC;gDAAsB,WAAU;;kEAC/B,8OAAC;wDAAI,WAAW,CAAC,0CAA0C,EAAE,QAAQ,KAAK,CAAC,mBAAmB,CAAC;;;;;;oDAC9F;;+CAFM;;;;;;;;;;kDAQb,8OAAC;wCAAI,WAAU;;4CACZ,QAAQ,YAAY,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,MAAM,0BAC3C,8OAAC;oDAEC,WAAU;8DAET;mDAHI;;;;;4CAMR,QAAQ,YAAY,CAAC,MAAM,GAAG,mBAC7B,8OAAC;gDAAK,WAAU;;oDAAyG;oDACrH,QAAQ,YAAY,CAAC,MAAM,GAAG;;;;;;;;;;;;;kDAMtC,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAM,QAAQ,IAAI;wCAClB,WAAU;;4CACX;0DAEC,8OAAC,iPAAA,CAAA,4BAAyB;gDAAC,WAAU;;;;;;;;;;;;kDAIvC,8OAAC;wCAAI,WAAW,CAAC,+CAA+C,EAAE,QAAQ,KAAK,CAAC,+EAA+E,CAAC;;;;;;;;;;;;2BA1D7J,QAAQ,EAAE;;;;;;;;;;8BAiErB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,WAAU;;sCAEV,8OAAC;4BAAE,WAAU;sCAAwC;;;;;;sCAGrD,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;;gCACX;8CAEC,8OAAC,iPAAA,CAAA,4BAAyB;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMjD", "debugId": null}}, {"offset": {"line": 1452, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/My%20Company%20Website/codnet-website/src/components/sections/PortfolioSection.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport Link from 'next/link';\nimport { useState } from 'react';\nimport { ArrowTopRightOnSquareIcon, EyeIcon } from '@heroicons/react/24/outline';\n\nconst categories = ['الكل', 'تطبيقات الهاتف', 'مواقع الويب', 'تطبيقات الحاسوب'];\n\nconst projects = [\n  {\n    id: 1,\n    title: 'تطبيق التجارة الإلكترونية',\n    category: 'تطبيقات الهاتف',\n    description: 'تطبيق متكامل للتسوق الإلكتروني مع نظام دفع آمن وتتبع الطلبات',\n    image: '/api/placeholder/400/300',\n    technologies: ['React Native', 'Node.js', 'MongoDB'],\n    client: 'شركة التجارة الذكية',\n    year: '2024',\n    results: ['زيادة المبيعات 150%', 'تحسين تجربة المستخدم', '50K+ تحميل'],\n    href: '/portfolio/ecommerce-app'\n  },\n  {\n    id: 2,\n    title: 'منصة التعلم الإلكتروني',\n    category: 'مواقع الويب',\n    description: 'منصة تعليمية تفاعلية مع نظام إدارة الدورات والاختبارات',\n    image: '/api/placeholder/400/300',\n    technologies: ['Next.js', 'PostgreSQL', 'WebRTC'],\n    client: 'معهد التقنية المتقدمة',\n    year: '2024',\n    results: ['10K+ طالب مسجل', 'تقييم 4.8/5', 'نمو 200%'],\n    href: '/portfolio/learning-platform'\n  },\n  {\n    id: 3,\n    title: 'نظام إدارة المخزون',\n    category: 'تطبيقات الحاسوب',\n    description: 'برنامج شامل لإدارة المخزون والمبيعات مع تقارير تحليلية',\n    image: '/api/placeholder/400/300',\n    technologies: ['Electron', 'React', 'SQLite'],\n    client: 'مجموعة الأعمال المتحدة',\n    year: '2023',\n    results: ['توفير 40% من الوقت', 'دقة 99.5%', 'ROI 300%'],\n    href: '/portfolio/inventory-system'\n  },\n  {\n    id: 4,\n    title: 'تطبيق الصحة واللياقة',\n    category: 'تطبيقات الهاتف',\n    description: 'تطبيق لتتبع اللياقة البدنية والصحة مع مدرب شخصي ذكي',\n    image: '/api/placeholder/400/300',\n    technologies: ['Flutter', 'Firebase', 'AI/ML'],\n    client: 'مركز اللياقة الذكي',\n    year: '2024',\n    results: ['100K+ مستخدم نشط', 'تقييم 4.9/5', 'نمو 180%'],\n    href: '/portfolio/fitness-app'\n  },\n  {\n    id: 5,\n    title: 'موقع الشركة المؤسسية',\n    category: 'مواقع الويب',\n    description: 'موقع مؤسسي متطور مع نظام إدارة المحتوى ومتعدد اللغات',\n    image: '/api/placeholder/400/300',\n    technologies: ['Next.js', 'Strapi', 'Tailwind'],\n    client: 'الشركة العالمية للاستثمار',\n    year: '2023',\n    results: ['زيادة الزيارات 250%', 'تحسين SEO', 'تقليل معدل الارتداد 60%'],\n    href: '/portfolio/corporate-website'\n  },\n  {\n    id: 6,\n    title: 'برنامج المحاسبة المتقدم',\n    category: 'تطبيقات الحاسوب',\n    description: 'نظام محاسبي شامل مع تقارير مالية وضريبية متقدمة',\n    image: '/api/placeholder/400/300',\n    technologies: ['C#', '.NET', 'SQL Server'],\n    client: 'مكتب المحاسبة المتخصص',\n    year: '2023',\n    results: ['أتمتة 90% من العمليات', 'دقة 99.9%', 'توفير 50% من الوقت'],\n    href: '/portfolio/accounting-software'\n  }\n];\n\nexport default function PortfolioSection() {\n  const [activeCategory, setActiveCategory] = useState('الكل');\n  const [hoveredProject, setHoveredProject] = useState<number | null>(null);\n\n  const filteredProjects = activeCategory === 'الكل' \n    ? projects \n    : projects.filter(project => project.category === activeCategory);\n\n  return (\n    <section className=\"py-20 bg-gray-50 dark:bg-slate-800\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Section Header */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          viewport={{ once: true }}\n          transition={{ duration: 0.6 }}\n          className=\"text-center mb-16\"\n        >\n          <h2 className=\"text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 dark:text-white mb-4\">\n            معرض <span className=\"gradient-text\">أعمالنا</span>\n          </h2>\n          <p className=\"text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto mb-8\">\n            اكتشف مجموعة من مشاريعنا الناجحة التي حققت نتائج استثنائية لعملائنا\n          </p>\n\n          {/* Category Filter */}\n          <div className=\"flex flex-wrap justify-center gap-4\">\n            {categories.map((category) => (\n              <button\n                key={category}\n                onClick={() => setActiveCategory(category)}\n                className={`px-6 py-3 rounded-full font-medium transition-all duration-300 ${\n                  activeCategory === category\n                    ? 'bg-blue-600 text-white shadow-lg'\n                    : 'bg-white dark:bg-slate-700 text-gray-700 dark:text-gray-300 hover:bg-blue-50 dark:hover:bg-slate-600'\n                }`}\n              >\n                {category}\n              </button>\n            ))}\n          </div>\n        </motion.div>\n\n        {/* Projects Grid */}\n        <motion.div\n          layout\n          className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-8\"\n        >\n          {filteredProjects.map((project, index) => (\n            <motion.div\n              key={project.id}\n              layout\n              initial={{ opacity: 0, scale: 0.9 }}\n              animate={{ opacity: 1, scale: 1 }}\n              exit={{ opacity: 0, scale: 0.9 }}\n              transition={{ duration: 0.5, delay: index * 0.1 }}\n              className=\"group relative bg-white dark:bg-slate-900 rounded-2xl overflow-hidden shadow-lg card-hover\"\n              onMouseEnter={() => setHoveredProject(project.id)}\n              onMouseLeave={() => setHoveredProject(null)}\n            >\n              {/* Project Image */}\n              <div className=\"relative h-48 bg-gradient-to-br from-blue-500 to-purple-600 overflow-hidden\">\n                {/* Placeholder for project image */}\n                <div className=\"absolute inset-0 bg-gradient-to-br from-blue-500/20 to-purple-600/20\" />\n                <div className=\"absolute inset-0 flex items-center justify-center\">\n                  <div className=\"text-white text-6xl opacity-20\">📱</div>\n                </div>\n                \n                {/* Overlay */}\n                <div className={`absolute inset-0 bg-black/50 transition-opacity duration-300 ${\n                  hoveredProject === project.id ? 'opacity-100' : 'opacity-0'\n                }`}>\n                  <div className=\"absolute inset-0 flex items-center justify-center\">\n                    <Link\n                      href={project.href}\n                      className=\"inline-flex items-center px-4 py-2 bg-white text-gray-900 rounded-lg font-medium hover:bg-gray-100 transition-colors duration-200\"\n                    >\n                      <EyeIcon className=\"h-5 w-5 ml-2\" />\n                      عرض المشروع\n                    </Link>\n                  </div>\n                </div>\n\n                {/* Category Badge */}\n                <div className=\"absolute top-4 right-4\">\n                  <span className=\"px-3 py-1 bg-white/90 text-gray-900 text-xs font-medium rounded-full\">\n                    {project.category}\n                  </span>\n                </div>\n              </div>\n\n              {/* Project Content */}\n              <div className=\"p-6\">\n                <div className=\"flex items-center justify-between mb-2\">\n                  <h3 className=\"text-xl font-bold text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-300\">\n                    {project.title}\n                  </h3>\n                  <span className=\"text-sm text-gray-500 dark:text-gray-400\">\n                    {project.year}\n                  </span>\n                </div>\n\n                <p className=\"text-gray-600 dark:text-gray-300 mb-4 leading-relaxed\">\n                  {project.description}\n                </p>\n\n                {/* Client */}\n                <p className=\"text-sm text-gray-500 dark:text-gray-400 mb-4\">\n                  العميل: {project.client}\n                </p>\n\n                {/* Technologies */}\n                <div className=\"flex flex-wrap gap-2 mb-4\">\n                  {project.technologies.map((tech, techIndex) => (\n                    <span\n                      key={techIndex}\n                      className=\"px-2 py-1 text-xs font-medium bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200 rounded-md\"\n                    >\n                      {tech}\n                    </span>\n                  ))}\n                </div>\n\n                {/* Results */}\n                <div className=\"space-y-1 mb-4\">\n                  {project.results.slice(0, 2).map((result, resultIndex) => (\n                    <div key={resultIndex} className=\"flex items-center text-sm text-gray-600 dark:text-gray-300\">\n                      <div className=\"w-1.5 h-1.5 rounded-full bg-green-500 ml-2 flex-shrink-0\" />\n                      {result}\n                    </div>\n                  ))}\n                </div>\n\n                {/* CTA */}\n                <Link\n                  href={project.href}\n                  className=\"inline-flex items-center text-sm font-semibold text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 transition-colors duration-300\"\n                >\n                  اقرأ دراسة الحالة\n                  <ArrowTopRightOnSquareIcon className=\"mr-1 h-4 w-4 group-hover:-translate-y-0.5 group-hover:translate-x-0.5 transition-transform duration-300\" />\n                </Link>\n              </div>\n            </motion.div>\n          ))}\n        </motion.div>\n\n        {/* Bottom CTA */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          viewport={{ once: true }}\n          transition={{ duration: 0.6, delay: 0.4 }}\n          className=\"text-center mt-16\"\n        >\n          <p className=\"text-gray-600 dark:text-gray-300 mb-6\">\n            هل تريد رؤية المزيد من أعمالنا؟\n          </p>\n          <Link\n            href=\"/portfolio\"\n            className=\"btn-primary inline-flex items-center px-8 py-4 text-white font-bold rounded-xl text-lg\"\n          >\n            عرض جميع المشاريع\n            <ArrowTopRightOnSquareIcon className=\"mr-2 h-5 w-5\" />\n          </Link>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AALA;;;;;;AAOA,MAAM,aAAa;IAAC;IAAQ;IAAkB;IAAe;CAAkB;AAE/E,MAAM,WAAW;IACf;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,aAAa;QACb,OAAO;QACP,cAAc;YAAC;YAAgB;YAAW;SAAU;QACpD,QAAQ;QACR,MAAM;QACN,SAAS;YAAC;YAAuB;YAAwB;SAAa;QACtE,MAAM;IACR;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,aAAa;QACb,OAAO;QACP,cAAc;YAAC;YAAW;YAAc;SAAS;QACjD,QAAQ;QACR,MAAM;QACN,SAAS;YAAC;YAAkB;YAAe;SAAW;QACtD,MAAM;IACR;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,aAAa;QACb,OAAO;QACP,cAAc;YAAC;YAAY;YAAS;SAAS;QAC7C,QAAQ;QACR,MAAM;QACN,SAAS;YAAC;YAAsB;YAAa;SAAW;QACxD,MAAM;IACR;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,aAAa;QACb,OAAO;QACP,cAAc;YAAC;YAAW;YAAY;SAAQ;QAC9C,QAAQ;QACR,MAAM;QACN,SAAS;YAAC;YAAoB;YAAe;SAAW;QACxD,MAAM;IACR;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,aAAa;QACb,OAAO;QACP,cAAc;YAAC;YAAW;YAAU;SAAW;QAC/C,QAAQ;QACR,MAAM;QACN,SAAS;YAAC;YAAuB;YAAa;SAA0B;QACxE,MAAM;IACR;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,aAAa;QACb,OAAO;QACP,cAAc;YAAC;YAAM;YAAQ;SAAa;QAC1C,QAAQ;QACR,MAAM;QACN,SAAS;YAAC;YAAyB;YAAa;SAAqB;QACrE,MAAM;IACR;CACD;AAEc,SAAS;IACtB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAEpE,MAAM,mBAAmB,mBAAmB,SACxC,WACA,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,QAAQ,KAAK;IAEpD,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;;sCAEV,8OAAC;4BAAG,WAAU;;gCAAgF;8CACvF,8OAAC;oCAAK,WAAU;8CAAgB;;;;;;;;;;;;sCAEvC,8OAAC;4BAAE,WAAU;sCAAkE;;;;;;sCAK/E,8OAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC,yBACf,8OAAC;oCAEC,SAAS,IAAM,kBAAkB;oCACjC,WAAW,CAAC,+DAA+D,EACzE,mBAAmB,WACf,qCACA,wGACJ;8CAED;mCARI;;;;;;;;;;;;;;;;8BAeb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,MAAM;oBACN,WAAU;8BAET,iBAAiB,GAAG,CAAC,CAAC,SAAS,sBAC9B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,MAAM;4BACN,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAI;4BAClC,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAE;4BAChC,MAAM;gCAAE,SAAS;gCAAG,OAAO;4BAAI;4BAC/B,YAAY;gCAAE,UAAU;gCAAK,OAAO,QAAQ;4BAAI;4BAChD,WAAU;4BACV,cAAc,IAAM,kBAAkB,QAAQ,EAAE;4BAChD,cAAc,IAAM,kBAAkB;;8CAGtC,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;0DAAiC;;;;;;;;;;;sDAIlD,8OAAC;4CAAI,WAAW,CAAC,6DAA6D,EAC5E,mBAAmB,QAAQ,EAAE,GAAG,gBAAgB,aAChD;sDACA,cAAA,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAM,QAAQ,IAAI;oDAClB,WAAU;;sEAEV,8OAAC,6MAAA,CAAA,UAAO;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;;;;;sDAO1C,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DACb,QAAQ,QAAQ;;;;;;;;;;;;;;;;;8CAMvB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DACX,QAAQ,KAAK;;;;;;8DAEhB,8OAAC;oDAAK,WAAU;8DACb,QAAQ,IAAI;;;;;;;;;;;;sDAIjB,8OAAC;4CAAE,WAAU;sDACV,QAAQ,WAAW;;;;;;sDAItB,8OAAC;4CAAE,WAAU;;gDAAgD;gDAClD,QAAQ,MAAM;;;;;;;sDAIzB,8OAAC;4CAAI,WAAU;sDACZ,QAAQ,YAAY,CAAC,GAAG,CAAC,CAAC,MAAM,0BAC/B,8OAAC;oDAEC,WAAU;8DAET;mDAHI;;;;;;;;;;sDASX,8OAAC;4CAAI,WAAU;sDACZ,QAAQ,OAAO,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,QAAQ,4BACxC,8OAAC;oDAAsB,WAAU;;sEAC/B,8OAAC;4DAAI,WAAU;;;;;;wDACd;;mDAFO;;;;;;;;;;sDAQd,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAM,QAAQ,IAAI;4CAClB,WAAU;;gDACX;8DAEC,8OAAC,iPAAA,CAAA,4BAAyB;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;2BAzFpC,QAAQ,EAAE;;;;;;;;;;8BAiGrB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,WAAU;;sCAEV,8OAAC;4BAAE,WAAU;sCAAwC;;;;;;sCAGrD,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;;gCACX;8CAEC,8OAAC,iPAAA,CAAA,4BAAyB;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMjD", "debugId": null}}, {"offset": {"line": 1961, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/My%20Company%20Website/codnet-website/src/components/sections/TestimonialsSection.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { useState, useEffect } from 'react';\nimport { ChevronLeftIcon, ChevronRightIcon, StarIcon } from '@heroicons/react/24/solid';\n\nconst testimonials = [\n  {\n    id: 1,\n    name: 'أحم<PERSON> محمد',\n    position: 'المدير التنفيذي',\n    company: 'شركة التجارة الذكية',\n    image: '/api/placeholder/80/80',\n    rating: 5,\n    text: 'فريق Codnet تجاوز توقعاتنا في تطوير تطبيق التجارة الإلكترونية. الجودة العالية والالتزام بالمواعيد جعلنا نحقق نمواً استثنائياً في المبيعات بنسبة 150%.',\n    project: 'تطبيق التجارة الإلكترونية',\n    results: ['زيادة المبيعات 150%', 'تحسين تجربة المستخدم', '50K+ تحميل']\n  },\n  {\n    id: 2,\n    name: 'فاطمة العلي',\n    position: 'مديرة التقنية',\n    company: 'معهد التقنية المتقدمة',\n    image: '/api/placeholder/80/80',\n    rating: 5,\n    text: 'منصة التعلم الإلكتروني التي طورها فريق Codnet غيرت طريقة تقديم التعليم في معهدنا. التصميم الرائع والوظائف المتقدمة ساعدتنا في الوصول لأكثر من 10 آلاف طالب.',\n    project: 'منصة التعلم الإلكتروني',\n    results: ['10K+ طالب مسجل', 'تقييم 4.8/5', 'نمو 200%']\n  },\n  {\n    id: 3,\n    name: 'خالد السعيد',\n    position: 'مالك الشركة',\n    company: 'مجموعة الأعمال المتحدة',\n    image: '/api/placeholder/80/80',\n    rating: 5,\n    text: 'نظام إدارة المخزون الذي طوره Codnet وفر علينا 40% من الوقت وحسن دقة العمليات إلى 99.5%. استثمار ممتاز حقق عائداً يفوق 300%.',\n    project: 'نظام إدارة المخزون',\n    results: ['توفير 40% من الوقت', 'دقة 99.5%', 'ROI 300%']\n  },\n  {\n    id: 4,\n    name: 'سارة أحمد',\n    position: 'مديرة التسويق',\n    company: 'مركز اللياقة الذكي',\n    image: '/api/placeholder/80/80',\n    rating: 5,\n    text: 'تطبيق اللياقة البدنية الذي طوره Codnet حقق نجاحاً باهراً. أكثر من 100 ألف مستخدم نشط وتقييم 4.9 من 5 نجوم. فريق محترف ومبدع.',\n    project: 'تطبيق الصحة واللياقة',\n    results: ['100K+ مستخدم نشط', 'تقييم 4.9/5', 'نمو 180%']\n  },\n  {\n    id: 5,\n    name: 'محمد الراشد',\n    position: 'مدير التطوير',\n    company: 'الشركة العالمية للاستثمار',\n    image: '/api/placeholder/80/80',\n    rating: 5,\n    text: 'الموقع المؤسسي الذي طوره Codnet رفع مستوى حضورنا الرقمي بشكل كبير. زيادة الزيارات بنسبة 250% وتحسين كبير في محركات البحث.',\n    project: 'الموقع المؤسسي',\n    results: ['زيادة الزيارات 250%', 'تحسين SEO', 'تقليل معدل الارتداد 60%']\n  }\n];\n\nexport default function TestimonialsSection() {\n  const [currentIndex, setCurrentIndex] = useState(0);\n  const [isAutoPlaying, setIsAutoPlaying] = useState(true);\n\n  // Auto-play functionality\n  useEffect(() => {\n    if (!isAutoPlaying) return;\n    \n    const interval = setInterval(() => {\n      setCurrentIndex((prev) => (prev + 1) % testimonials.length);\n    }, 5000);\n\n    return () => clearInterval(interval);\n  }, [isAutoPlaying]);\n\n  const nextTestimonial = () => {\n    setCurrentIndex((prev) => (prev + 1) % testimonials.length);\n    setIsAutoPlaying(false);\n  };\n\n  const prevTestimonial = () => {\n    setCurrentIndex((prev) => (prev - 1 + testimonials.length) % testimonials.length);\n    setIsAutoPlaying(false);\n  };\n\n  const goToTestimonial = (index: number) => {\n    setCurrentIndex(index);\n    setIsAutoPlaying(false);\n  };\n\n  return (\n    <section className=\"py-20 bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-slate-900 dark:via-blue-900 dark:to-indigo-900 relative overflow-hidden\">\n      {/* Background Elements */}\n      <div className=\"absolute inset-0\">\n        <div className=\"absolute top-20 left-10 w-72 h-72 bg-blue-400/20 rounded-full blur-3xl animate-pulse\" />\n        <div className=\"absolute bottom-20 right-10 w-96 h-96 bg-purple-400/20 rounded-full blur-3xl animate-pulse\" />\n      </div>\n\n      <div className=\"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Section Header */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          viewport={{ once: true }}\n          transition={{ duration: 0.6 }}\n          className=\"text-center mb-16\"\n        >\n          <h2 className=\"text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 dark:text-white mb-4\">\n            ماذا يقول <span className=\"gradient-text\">عملاؤنا</span>\n          </h2>\n          <p className=\"text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto\">\n            شهادات حقيقية من عملائنا الذين حققوا نجاحات استثنائية مع حلولنا التقنية\n          </p>\n        </motion.div>\n\n        {/* Main Testimonial */}\n        <div className=\"relative max-w-4xl mx-auto\">\n          <motion.div\n            key={currentIndex}\n            initial={{ opacity: 0, x: 50 }}\n            animate={{ opacity: 1, x: 0 }}\n            exit={{ opacity: 0, x: -50 }}\n            transition={{ duration: 0.5 }}\n            className=\"bg-white dark:bg-slate-800 rounded-3xl p-8 md:p-12 shadow-2xl border border-gray-100 dark:border-gray-700\"\n          >\n            {/* Quote Icon */}\n            <div className=\"text-6xl text-blue-600/20 dark:text-blue-400/20 mb-6\">\n              \"\n            </div>\n\n            {/* Rating */}\n            <div className=\"flex items-center mb-6\">\n              {[...Array(testimonials[currentIndex].rating)].map((_, i) => (\n                <StarIcon key={i} className=\"h-5 w-5 text-yellow-400\" />\n              ))}\n            </div>\n\n            {/* Testimonial Text */}\n            <blockquote className=\"text-lg md:text-xl text-gray-700 dark:text-gray-300 leading-relaxed mb-8\">\n              {testimonials[currentIndex].text}\n            </blockquote>\n\n            {/* Results */}\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 mb-8\">\n              {testimonials[currentIndex].results.map((result, index) => (\n                <div key={index} className=\"flex items-center text-sm text-gray-600 dark:text-gray-400\">\n                  <div className=\"w-2 h-2 rounded-full bg-green-500 ml-2 flex-shrink-0\" />\n                  {result}\n                </div>\n              ))}\n            </div>\n\n            {/* Client Info */}\n            <div className=\"flex items-center\">\n              <div className=\"w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold text-xl ml-4\">\n                {testimonials[currentIndex].name.charAt(0)}\n              </div>\n              <div>\n                <h4 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                  {testimonials[currentIndex].name}\n                </h4>\n                <p className=\"text-gray-600 dark:text-gray-400\">\n                  {testimonials[currentIndex].position}\n                </p>\n                <p className=\"text-blue-600 dark:text-blue-400 font-medium\">\n                  {testimonials[currentIndex].company}\n                </p>\n              </div>\n            </div>\n\n            {/* Project Badge */}\n            <div className=\"absolute top-8 left-8\">\n              <span className=\"px-3 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200 text-sm font-medium rounded-full\">\n                {testimonials[currentIndex].project}\n              </span>\n            </div>\n          </motion.div>\n\n          {/* Navigation Buttons */}\n          <button\n            onClick={prevTestimonial}\n            className=\"absolute top-1/2 -translate-y-1/2 -right-6 w-12 h-12 bg-white dark:bg-slate-800 rounded-full shadow-lg flex items-center justify-center text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200\"\n          >\n            <ChevronRightIcon className=\"h-6 w-6\" />\n          </button>\n          \n          <button\n            onClick={nextTestimonial}\n            className=\"absolute top-1/2 -translate-y-1/2 -left-6 w-12 h-12 bg-white dark:bg-slate-800 rounded-full shadow-lg flex items-center justify-center text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200\"\n          >\n            <ChevronLeftIcon className=\"h-6 w-6\" />\n          </button>\n        </div>\n\n        {/* Dots Indicator */}\n        <div className=\"flex justify-center mt-8 space-x-2 rtl:space-x-reverse\">\n          {testimonials.map((_, index) => (\n            <button\n              key={index}\n              onClick={() => goToTestimonial(index)}\n              className={`w-3 h-3 rounded-full transition-all duration-300 ${\n                index === currentIndex\n                  ? 'bg-blue-600 dark:bg-blue-400 w-8'\n                  : 'bg-gray-300 dark:bg-gray-600 hover:bg-gray-400 dark:hover:bg-gray-500'\n              }`}\n            />\n          ))}\n        </div>\n\n        {/* Thumbnails */}\n        <div className=\"flex justify-center mt-12 space-x-4 rtl:space-x-reverse overflow-x-auto pb-4\">\n          {testimonials.map((testimonial, index) => (\n            <button\n              key={testimonial.id}\n              onClick={() => goToTestimonial(index)}\n              className={`flex-shrink-0 p-4 rounded-xl transition-all duration-300 ${\n                index === currentIndex\n                  ? 'bg-blue-100 dark:bg-blue-900/30 border-2 border-blue-600 dark:border-blue-400'\n                  : 'bg-white dark:bg-slate-800 border border-gray-200 dark:border-gray-700 hover:border-blue-300 dark:hover:border-blue-600'\n              }`}\n            >\n              <div className=\"flex items-center space-x-3 rtl:space-x-reverse\">\n                <div className=\"w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold text-sm\">\n                  {testimonial.name.charAt(0)}\n                </div>\n                <div className=\"text-right\">\n                  <p className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                    {testimonial.name}\n                  </p>\n                  <p className=\"text-xs text-gray-600 dark:text-gray-400\">\n                    {testimonial.company}\n                  </p>\n                </div>\n              </div>\n            </button>\n          ))}\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAJA;;;;;AAMA,MAAM,eAAe;IACnB;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,SAAS;QACT,OAAO;QACP,QAAQ;QACR,MAAM;QACN,SAAS;QACT,SAAS;YAAC;YAAuB;YAAwB;SAAa;IACxE;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,SAAS;QACT,OAAO;QACP,QAAQ;QACR,MAAM;QACN,SAAS;QACT,SAAS;YAAC;YAAkB;YAAe;SAAW;IACxD;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,SAAS;QACT,OAAO;QACP,QAAQ;QACR,MAAM;QACN,SAAS;QACT,SAAS;YAAC;YAAsB;YAAa;SAAW;IAC1D;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,SAAS;QACT,OAAO;QACP,QAAQ;QACR,MAAM;QACN,SAAS;QACT,SAAS;YAAC;YAAoB;YAAe;SAAW;IAC1D;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,SAAS;QACT,OAAO;QACP,QAAQ;QACR,MAAM;QACN,SAAS;QACT,SAAS;YAAC;YAAuB;YAAa;SAA0B;IAC1E;CACD;AAEc,SAAS;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,0BAA0B;IAC1B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,eAAe;QAEpB,MAAM,WAAW,YAAY;YAC3B,gBAAgB,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,aAAa,MAAM;QAC5D,GAAG;QAEH,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC;KAAc;IAElB,MAAM,kBAAkB;QACtB,gBAAgB,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,aAAa,MAAM;QAC1D,iBAAiB;IACnB;IAEA,MAAM,kBAAkB;QACtB,gBAAgB,CAAC,OAAS,CAAC,OAAO,IAAI,aAAa,MAAM,IAAI,aAAa,MAAM;QAChF,iBAAiB;IACnB;IAEA,MAAM,kBAAkB,CAAC;QACvB,gBAAgB;QAChB,iBAAiB;IACnB;IAEA,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,UAAU;4BAAE,MAAM;wBAAK;wBACvB,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,WAAU;;0CAEV,8OAAC;gCAAG,WAAU;;oCAAgF;kDAClF,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;;;;;;;0CAE5C,8OAAC;gCAAE,WAAU;0CAA6D;;;;;;;;;;;;kCAM5E,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,MAAM;oCAAE,SAAS;oCAAG,GAAG,CAAC;gCAAG;gCAC3B,YAAY;oCAAE,UAAU;gCAAI;gCAC5B,WAAU;;kDAGV,8OAAC;wCAAI,WAAU;kDAAuD;;;;;;kDAKtE,8OAAC;wCAAI,WAAU;kDACZ;+CAAI,MAAM,YAAY,CAAC,aAAa,CAAC,MAAM;yCAAE,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrD,8OAAC,6MAAA,CAAA,WAAQ;gDAAS,WAAU;+CAAb;;;;;;;;;;kDAKnB,8OAAC;wCAAW,WAAU;kDACnB,YAAY,CAAC,aAAa,CAAC,IAAI;;;;;;kDAIlC,8OAAC;wCAAI,WAAU;kDACZ,YAAY,CAAC,aAAa,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,sBAC/C,8OAAC;gDAAgB,WAAU;;kEACzB,8OAAC;wDAAI,WAAU;;;;;;oDACd;;+CAFO;;;;;;;;;;kDAQd,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACZ,YAAY,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC;;;;;;0DAE1C,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEACX,YAAY,CAAC,aAAa,CAAC,IAAI;;;;;;kEAElC,8OAAC;wDAAE,WAAU;kEACV,YAAY,CAAC,aAAa,CAAC,QAAQ;;;;;;kEAEtC,8OAAC;wDAAE,WAAU;kEACV,YAAY,CAAC,aAAa,CAAC,OAAO;;;;;;;;;;;;;;;;;;kDAMzC,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;sDACb,YAAY,CAAC,aAAa,CAAC,OAAO;;;;;;;;;;;;+BAvDlC;;;;;0CA6DP,8OAAC;gCACC,SAAS;gCACT,WAAU;0CAEV,cAAA,8OAAC,6NAAA,CAAA,mBAAgB;oCAAC,WAAU;;;;;;;;;;;0CAG9B,8OAAC;gCACC,SAAS;gCACT,WAAU;0CAEV,cAAA,8OAAC,2NAAA,CAAA,kBAAe;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAK/B,8OAAC;wBAAI,WAAU;kCACZ,aAAa,GAAG,CAAC,CAAC,GAAG,sBACpB,8OAAC;gCAEC,SAAS,IAAM,gBAAgB;gCAC/B,WAAW,CAAC,iDAAiD,EAC3D,UAAU,eACN,qCACA,yEACJ;+BANG;;;;;;;;;;kCAYX,8OAAC;wBAAI,WAAU;kCACZ,aAAa,GAAG,CAAC,CAAC,aAAa,sBAC9B,8OAAC;gCAEC,SAAS,IAAM,gBAAgB;gCAC/B,WAAW,CAAC,yDAAyD,EACnE,UAAU,eACN,kFACA,2HACJ;0CAEF,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACZ,YAAY,IAAI,CAAC,MAAM,CAAC;;;;;;sDAE3B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DACV,YAAY,IAAI;;;;;;8DAEnB,8OAAC;oDAAE,WAAU;8DACV,YAAY,OAAO;;;;;;;;;;;;;;;;;;+BAjBrB,YAAY,EAAE;;;;;;;;;;;;;;;;;;;;;;AA2BjC", "debugId": null}}, {"offset": {"line": 2429, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/My%20Company%20Website/codnet-website/src/components/sections/WhyChooseUsSection.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { \n  LightBulbIcon,\n  ShieldCheckIcon,\n  ClockIcon,\n  UserGroupIcon,\n  CogIcon,\n  TrophyIcon,\n  HeartIcon,\n  RocketLaunchIcon\n} from '@heroicons/react/24/outline';\n\nconst features = [\n  {\n    icon: LightBulbIcon,\n    title: 'الابتكار والإبداع',\n    description: 'نستخدم أحدث التقنيات والأساليب المبتكرة لتطوير حلول فريدة تميز عملك عن المنافسين',\n    color: 'from-yellow-500 to-orange-500'\n  },\n  {\n    icon: ShieldCheckIcon,\n    title: 'الجودة والموثوقية',\n    description: 'نلتزم بأعلى معايير الجودة في كل مرحلة من مراحل التطوير مع ضمان الأمان والاستقرار',\n    color: 'from-green-500 to-emerald-500'\n  },\n  {\n    icon: ClockIcon,\n    title: 'التسليم في الوقت المحدد',\n    description: 'نحترم المواعيد المتفق عليها ونضمن تسليم مشروعك في الوقت المحدد دون التنازل عن الجودة',\n    color: 'from-blue-500 to-cyan-500'\n  },\n  {\n    icon: UserGroupIcon,\n    title: 'فريق خبراء متخصص',\n    description: 'فريقنا من المطورين والمصممين ذوي الخبرة العالية يضمن تنفيذ مشروعك بأفضل الممارسات',\n    color: 'from-purple-500 to-pink-500'\n  },\n  {\n    icon: CogIcon,\n    title: 'دعم فني مستمر',\n    description: 'نقدم دعماً فنياً شاملاً ومستمراً حتى بعد تسليم المشروع لضمان استمرارية العمل بكفاءة',\n    color: 'from-red-500 to-rose-500'\n  },\n  {\n    icon: TrophyIcon,\n    title: 'سجل حافل بالنجاحات',\n    description: 'أكثر من 100 مشروع ناجح و50+ عميل راضي يشهدون على جودة خدماتنا وتميز أدائنا',\n    color: 'from-amber-500 to-yellow-500'\n  },\n  {\n    icon: HeartIcon,\n    title: 'شراكة طويلة المدى',\n    description: 'نؤمن ببناء علاقات شراكة قوية مع عملائنا تتجاوز حدود المشروع الواحد',\n    color: 'from-pink-500 to-red-500'\n  },\n  {\n    icon: RocketLaunchIcon,\n    title: 'نمو وتطوير مستمر',\n    description: 'نساعد عملاءنا على النمو والتطور من خلال حلول قابلة للتوسع ومواكبة للتطورات التقنية',\n    color: 'from-indigo-500 to-purple-500'\n  }\n];\n\nconst stats = [\n  { number: '100+', label: 'مشروع مكتمل', icon: '🚀' },\n  { number: '50+', label: 'عميل راضي', icon: '😊' },\n  { number: '5+', label: 'سنوات خبرة', icon: '⭐' },\n  { number: '99%', label: 'معدل رضا العملاء', icon: '💯' }\n];\n\nconst containerVariants = {\n  hidden: { opacity: 0 },\n  visible: {\n    opacity: 1,\n    transition: {\n      staggerChildren: 0.1\n    }\n  }\n};\n\nconst itemVariants = {\n  hidden: { opacity: 0, y: 30 },\n  visible: {\n    opacity: 1,\n    y: 0,\n    transition: {\n      duration: 0.6,\n      ease: \"easeOut\"\n    }\n  }\n};\n\nexport default function WhyChooseUsSection() {\n  return (\n    <section className=\"py-20 bg-white dark:bg-slate-900 relative overflow-hidden\">\n      {/* Background Elements */}\n      <div className=\"absolute inset-0\">\n        <div className=\"absolute top-20 left-10 w-72 h-72 bg-blue-400/10 rounded-full blur-3xl\" />\n        <div className=\"absolute bottom-20 right-10 w-96 h-96 bg-purple-400/10 rounded-full blur-3xl\" />\n      </div>\n\n      <div className=\"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Section Header */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          viewport={{ once: true }}\n          transition={{ duration: 0.6 }}\n          className=\"text-center mb-16\"\n        >\n          <h2 className=\"text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 dark:text-white mb-4\">\n            لماذا تختار <span className=\"gradient-text\">Codnet</span>؟\n          </h2>\n          <p className=\"text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto\">\n            نحن لا نطور مجرد تطبيقات، بل نبني شراكات نجاح طويلة المدى مع عملائنا\n          </p>\n        </motion.div>\n\n        {/* Stats Section */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          viewport={{ once: true }}\n          transition={{ duration: 0.6, delay: 0.2 }}\n          className=\"grid grid-cols-2 md:grid-cols-4 gap-8 mb-20\"\n        >\n          {stats.map((stat, index) => (\n            <motion.div\n              key={stat.label}\n              initial={{ opacity: 0, scale: 0.8 }}\n              whileInView={{ opacity: 1, scale: 1 }}\n              viewport={{ once: true }}\n              transition={{ duration: 0.6, delay: 0.3 + index * 0.1 }}\n              className=\"text-center p-6 bg-gray-50 dark:bg-slate-800 rounded-2xl hover:shadow-lg transition-shadow duration-300\"\n            >\n              <div className=\"text-4xl mb-2\">{stat.icon}</div>\n              <div className=\"text-2xl md:text-3xl font-bold text-blue-600 dark:text-blue-400 mb-1\">\n                {stat.number}\n              </div>\n              <div className=\"text-sm text-gray-600 dark:text-gray-400\">\n                {stat.label}\n              </div>\n            </motion.div>\n          ))}\n        </motion.div>\n\n        {/* Features Grid */}\n        <motion.div\n          variants={containerVariants}\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={{ once: true }}\n          className=\"grid md:grid-cols-2 lg:grid-cols-4 gap-8\"\n        >\n          {features.map((feature, index) => (\n            <motion.div\n              key={feature.title}\n              variants={itemVariants}\n              className=\"group relative p-6 bg-gray-50 dark:bg-slate-800 rounded-2xl hover:bg-white dark:hover:bg-slate-700 card-hover\"\n            >\n              {/* Icon */}\n              <div className={`inline-flex p-4 rounded-2xl bg-gradient-to-br ${feature.color} text-white mb-4 group-hover:scale-110 transition-transform duration-300 shadow-lg floating-animation`}>\n                <feature.icon className=\"h-6 w-6\" />\n              </div>\n\n              {/* Content */}\n              <h3 className=\"text-lg font-bold text-gray-900 dark:text-white mb-3 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-300\">\n                {feature.title}\n              </h3>\n              <p className=\"text-gray-600 dark:text-gray-300 text-sm leading-relaxed\">\n                {feature.description}\n              </p>\n\n              {/* Hover effect */}\n              <div className={`absolute inset-0 rounded-2xl bg-gradient-to-br ${feature.color} opacity-0 group-hover:opacity-5 transition-opacity duration-300`} />\n            </motion.div>\n          ))}\n        </motion.div>\n\n        {/* Process Section */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          viewport={{ once: true }}\n          transition={{ duration: 0.6, delay: 0.4 }}\n          className=\"mt-20 text-center\"\n        >\n          <h3 className=\"text-2xl md:text-3xl font-bold text-gray-900 dark:text-white mb-8\">\n            منهجية العمل المتطورة\n          </h3>\n          \n          <div className=\"grid md:grid-cols-4 gap-8\">\n            {[\n              { step: '01', title: 'التحليل والتخطيط', description: 'دراسة شاملة لمتطلباتك وأهدافك' },\n              { step: '02', title: 'التصميم والنمذجة', description: 'تصميم واجهات مستخدم جذابة وسهلة الاستخدام' },\n              { step: '03', title: 'التطوير والبرمجة', description: 'تطوير الحل باستخدام أحدث التقنيات' },\n              { step: '04', title: 'الاختبار والتسليم', description: 'اختبارات شاملة وتسليم المشروع مع الدعم' }\n            ].map((process, index) => (\n              <motion.div\n                key={process.step}\n                initial={{ opacity: 0, y: 30 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                viewport={{ once: true }}\n                transition={{ duration: 0.6, delay: 0.5 + index * 0.1 }}\n                className=\"relative\"\n              >\n                <div className=\"text-4xl font-bold text-blue-600/20 dark:text-blue-400/20 mb-4\">\n                  {process.step}\n                </div>\n                <h4 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-2\">\n                  {process.title}\n                </h4>\n                <p className=\"text-gray-600 dark:text-gray-300 text-sm\">\n                  {process.description}\n                </p>\n                \n                {/* Connector line */}\n                {index < 3 && (\n                  <div className=\"hidden md:block absolute top-8 left-full w-full h-0.5 bg-gradient-to-r from-blue-600/50 to-transparent\" />\n                )}\n              </motion.div>\n            ))}\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAcA,MAAM,WAAW;IACf;QACE,MAAM,yNAAA,CAAA,gBAAa;QACnB,OAAO;QACP,aAAa;QACb,OAAO;IACT;IACA;QACE,MAAM,6NAAA,CAAA,kBAAe;QACrB,OAAO;QACP,aAAa;QACb,OAAO;IACT;IACA;QACE,MAAM,iNAAA,CAAA,YAAS;QACf,OAAO;QACP,aAAa;QACb,OAAO;IACT;IACA;QACE,MAAM,yNAAA,CAAA,gBAAa;QACnB,OAAO;QACP,aAAa;QACb,OAAO;IACT;IACA;QACE,MAAM,6MAAA,CAAA,UAAO;QACb,OAAO;QACP,aAAa;QACb,OAAO;IACT;IACA;QACE,MAAM,mNAAA,CAAA,aAAU;QAChB,OAAO;QACP,aAAa;QACb,OAAO;IACT;IACA;QACE,MAAM,iNAAA,CAAA,YAAS;QACf,OAAO;QACP,aAAa;QACb,OAAO;IACT;IACA;QACE,MAAM,+NAAA,CAAA,mBAAgB;QACtB,OAAO;QACP,aAAa;QACb,OAAO;IACT;CACD;AAED,MAAM,QAAQ;IACZ;QAAE,QAAQ;QAAQ,OAAO;QAAe,MAAM;IAAK;IACnD;QAAE,QAAQ;QAAO,OAAO;QAAa,MAAM;IAAK;IAChD;QAAE,QAAQ;QAAM,OAAO;QAAc,MAAM;IAAI;IAC/C;QAAE,QAAQ;QAAO,OAAO;QAAoB,MAAM;IAAK;CACxD;AAED,MAAM,oBAAoB;IACxB,QAAQ;QAAE,SAAS;IAAE;IACrB,SAAS;QACP,SAAS;QACT,YAAY;YACV,iBAAiB;QACnB;IACF;AACF;AAEA,MAAM,eAAe;IACnB,QAAQ;QAAE,SAAS;QAAG,GAAG;IAAG;IAC5B,SAAS;QACP,SAAS;QACT,GAAG;QACH,YAAY;YACV,UAAU;YACV,MAAM;QACR;IACF;AACF;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,UAAU;4BAAE,MAAM;wBAAK;wBACvB,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,WAAU;;0CAEV,8OAAC;gCAAG,WAAU;;oCAAgF;kDAChF,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;oCAAa;;;;;;;0CAE3D,8OAAC;gCAAE,WAAU;0CAA6D;;;;;;;;;;;;kCAM5E,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,UAAU;4BAAE,MAAM;wBAAK;wBACvB,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,WAAU;kCAET,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAI;gCAClC,aAAa;oCAAE,SAAS;oCAAG,OAAO;gCAAE;gCACpC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,YAAY;oCAAE,UAAU;oCAAK,OAAO,MAAM,QAAQ;gCAAI;gCACtD,WAAU;;kDAEV,8OAAC;wCAAI,WAAU;kDAAiB,KAAK,IAAI;;;;;;kDACzC,8OAAC;wCAAI,WAAU;kDACZ,KAAK,MAAM;;;;;;kDAEd,8OAAC;wCAAI,WAAU;kDACZ,KAAK,KAAK;;;;;;;+BAZR,KAAK,KAAK;;;;;;;;;;kCAmBrB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,UAAU;wBACV,SAAQ;wBACR,aAAY;wBACZ,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;kCAET,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,UAAU;gCACV,WAAU;;kDAGV,8OAAC;wCAAI,WAAW,CAAC,8CAA8C,EAAE,QAAQ,KAAK,CAAC,qGAAqG,CAAC;kDACnL,cAAA,8OAAC,QAAQ,IAAI;4CAAC,WAAU;;;;;;;;;;;kDAI1B,8OAAC;wCAAG,WAAU;kDACX,QAAQ,KAAK;;;;;;kDAEhB,8OAAC;wCAAE,WAAU;kDACV,QAAQ,WAAW;;;;;;kDAItB,8OAAC;wCAAI,WAAW,CAAC,+CAA+C,EAAE,QAAQ,KAAK,CAAC,gEAAgE,CAAC;;;;;;;+BAlB5I,QAAQ,KAAK;;;;;;;;;;kCAwBxB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,UAAU;4BAAE,MAAM;wBAAK;wBACvB,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,WAAU;;0CAEV,8OAAC;gCAAG,WAAU;0CAAoE;;;;;;0CAIlF,8OAAC;gCAAI,WAAU;0CACZ;oCACC;wCAAE,MAAM;wCAAM,OAAO;wCAAoB,aAAa;oCAAgC;oCACtF;wCAAE,MAAM;wCAAM,OAAO;wCAAoB,aAAa;oCAA4C;oCAClG;wCAAE,MAAM;wCAAM,OAAO;wCAAoB,aAAa;oCAAoC;oCAC1F;wCAAE,MAAM;wCAAM,OAAO;wCAAqB,aAAa;oCAAyC;iCACjG,CAAC,GAAG,CAAC,CAAC,SAAS,sBACd,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,aAAa;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAChC,UAAU;4CAAE,MAAM;wCAAK;wCACvB,YAAY;4CAAE,UAAU;4CAAK,OAAO,MAAM,QAAQ;wCAAI;wCACtD,WAAU;;0DAEV,8OAAC;gDAAI,WAAU;0DACZ,QAAQ,IAAI;;;;;;0DAEf,8OAAC;gDAAG,WAAU;0DACX,QAAQ,KAAK;;;;;;0DAEhB,8OAAC;gDAAE,WAAU;0DACV,QAAQ,WAAW;;;;;;4CAIrB,QAAQ,mBACP,8OAAC;gDAAI,WAAU;;;;;;;uCAnBZ,QAAQ,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4BjC", "debugId": null}}, {"offset": {"line": 2887, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/My%20Company%20Website/codnet-website/src/components/sections/CTASection.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport Link from 'next/link';\r\nimport { motion } from 'framer-motion';\r\nimport { ArrowLeftIcon } from '@heroicons/react/24/outline';\r\n\r\nexport default function CTASection() {\r\n  return (\r\n    <section className=\"py-20 bg-gradient-to-br from-blue-900 via-indigo-900 to-purple-900 text-white relative overflow-hidden\">\r\n      <div className=\"absolute inset-0\">\r\n        <motion.div\r\n          className=\"absolute top-20 left-10 w-72 h-72 bg-blue-400/20 rounded-full blur-3xl\"\r\n          animate={{\r\n            x: [0, 100, 0],\r\n            y: [0, -50, 0],\r\n          }}\r\n          transition={{\r\n            duration: 20,\r\n            repeat: Infinity,\r\n            ease: \"linear\"\r\n          }}\r\n        />\r\n        <motion.div\r\n          className=\"absolute bottom-20 right-10 w-96 h-96 bg-purple-400/20 rounded-full blur-3xl\"\r\n          animate={{\r\n            x: [0, -80, 0],\r\n            y: [0, 60, 0],\r\n          }}\r\n          transition={{\r\n            duration: 25,\r\n            repeat: Infinity,\r\n            ease: \"linear\"\r\n          }}\r\n        />\r\n      </div>\r\n\r\n      <div className=\"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 30 }}\r\n          whileInView={{ opacity: 1, y: 0 }}\r\n          viewport={{ once: true }}\r\n          transition={{ duration: 0.6 }}\r\n        >\r\n          <h2 className=\"text-3xl md:text-4xl lg:text-5xl font-bold mb-6 leading-tight\">\r\n            هل أنت مستعد لتحويل{' '}\r\n            <span className=\"bg-gradient-to-r from-yellow-400 to-orange-400 bg-clip-text text-transparent\">\r\n              فكرتك\r\n            </span>\r\n            {' '}إلى واقع؟\r\n          </h2>\r\n          <p className=\"text-xl mb-8 text-blue-100 max-w-3xl mx-auto\">\r\n            انضم إلى أكثر من 50 عميل راضي واكتشف كيف يمكن لحلولنا التقنية المبتكرة أن تدفع نمو عملك إلى آفاق جديدة\r\n          </p>\r\n\r\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center items-center mb-8\">\r\n            <Link\r\n              href=\"/contact\"\r\n              className=\"btn-secondary inline-flex items-center px-10 py-5 text-white font-bold rounded-xl text-xl shadow-2xl\"\r\n            >\r\n              احصل على استشارة مجانية\r\n              <ArrowLeftIcon className=\"mr-2 h-6 w-6\" />\r\n            </Link>\r\n\r\n            <div className=\"flex items-center space-x-6 rtl:space-x-reverse text-blue-200 text-sm\">\r\n              <div className=\"flex items-center\">\r\n                <span className=\"text-yellow-400 ml-1\">⭐</span>\r\n                تقييم 5/5 نجوم\r\n              </div>\r\n              <div className=\"flex items-center\">\r\n                <span className=\"text-green-400 ml-1\">✓</span>\r\n                ضمان الجودة\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto\">\r\n            {[\r\n              { icon: '📞', title: 'اتصل بنا', desc: '+966 50 123 4567' },\r\n              { icon: '✉️', title: 'راسلنا', desc: '<EMAIL>' },\r\n              { icon: '📍', title: 'زورنا', desc: 'الرياض، السعودية' }\r\n            ].map((contact, index) => (\r\n              <motion.div\r\n                key={contact.title}\r\n                initial={{ opacity: 0, y: 30 }}\r\n                whileInView={{ opacity: 1, y: 0 }}\r\n                viewport={{ once: true }}\r\n                transition={{ duration: 0.6, delay: 0.2 + index * 0.1 }}\r\n                className=\"p-4 bg-white/10 backdrop-blur-sm rounded-xl border border-white/20\"\r\n              >\r\n                <div className=\"text-2xl mb-2\">{contact.icon}</div>\r\n                <h3 className=\"font-semibold mb-1\">{contact.title}</h3>\r\n                <p className=\"text-blue-200 text-sm\">{contact.desc}</p>\r\n              </motion.div>\r\n            ))}\r\n          </div>\r\n        </motion.div>\r\n      </div>\r\n    </section>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMe,SAAS;IACtB,qBACE,8OAAC;QAAQ,WAAU;;0BACjB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BACP,GAAG;gCAAC;gCAAG;gCAAK;6BAAE;4BACd,GAAG;gCAAC;gCAAG,CAAC;gCAAI;6BAAE;wBAChB;wBACA,YAAY;4BACV,UAAU;4BACV,QAAQ;4BACR,MAAM;wBACR;;;;;;kCAEF,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BACP,GAAG;gCAAC;gCAAG,CAAC;gCAAI;6BAAE;4BACd,GAAG;gCAAC;gCAAG;gCAAI;6BAAE;wBACf;wBACA,YAAY;4BACV,UAAU;4BACV,QAAQ;4BACR,MAAM;wBACR;;;;;;;;;;;;0BAIJ,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,YAAY;wBAAE,UAAU;oBAAI;;sCAE5B,8OAAC;4BAAG,WAAU;;gCAAgE;gCACxD;8CACpB,8OAAC;oCAAK,WAAU;8CAA+E;;;;;;gCAG9F;gCAAI;;;;;;;sCAEP,8OAAC;4BAAE,WAAU;sCAA+C;;;;;;sCAI5D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;;wCACX;sDAEC,8OAAC,yNAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;;;;;;;8CAG3B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAuB;;;;;;gDAAQ;;;;;;;sDAGjD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAsB;;;;;;gDAAQ;;;;;;;;;;;;;;;;;;;sCAMpD,8OAAC;4BAAI,WAAU;sCACZ;gCACC;oCAAE,MAAM;oCAAM,OAAO;oCAAY,MAAM;gCAAmB;gCAC1D;oCAAE,MAAM;oCAAM,OAAO;oCAAU,MAAM;gCAAkB;gCACvD;oCAAE,MAAM;oCAAM,OAAO;oCAAS,MAAM;gCAAmB;6BACxD,CAAC,GAAG,CAAC,CAAC,SAAS,sBACd,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,UAAU;wCAAE,MAAM;oCAAK;oCACvB,YAAY;wCAAE,UAAU;wCAAK,OAAO,MAAM,QAAQ;oCAAI;oCACtD,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;sDAAiB,QAAQ,IAAI;;;;;;sDAC5C,8OAAC;4CAAG,WAAU;sDAAsB,QAAQ,KAAK;;;;;;sDACjD,8OAAC;4CAAE,WAAU;sDAAyB,QAAQ,IAAI;;;;;;;mCAT7C,QAAQ,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiBlC", "debugId": null}}]}