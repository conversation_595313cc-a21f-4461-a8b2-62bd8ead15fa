'use client';

import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import { motion } from 'framer-motion';
import { 
  PhoneIcon, 
  EnvelopeIcon, 
  MapPinIcon,
  ClockIcon 
} from '@heroicons/react/24/outline';

const contactInfo = [
  {
    icon: PhoneIcon,
    title: 'اتصل بنا',
    details: ['+966 50 123 4567', '+966 11 234 5678'],
    color: 'from-green-500 to-emerald-500'
  },
  {
    icon: EnvelopeIcon,
    title: 'راسلنا',
    details: ['<EMAIL>', '<EMAIL>'],
    color: 'from-blue-500 to-cyan-500'
  },
  {
    icon: MapPinIcon,
    title: 'زورنا',
    details: ['الرياض، المملكة العربية السعودية', 'حي الملك فهد، طريق الملك عبدالعزيز'],
    color: 'from-purple-500 to-pink-500'
  },
  {
    icon: ClockIcon,
    title: 'ساعات العمل',
    details: ['الأحد - الخميس: 9:00 ص - 6:00 م', 'الجمعة - السبت: مغلق'],
    color: 'from-amber-500 to-orange-500'
  }
];

export default function ContactPage() {
  return (
    <div className="min-h-screen">
      <Header />
      <main className="pt-20">
        {/* Hero Section */}
        <section className="py-20 bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-slate-900 dark:via-blue-900 dark:to-indigo-900">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-6">
                تواصل معنا
              </h1>
              <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                نحن هنا لمساعدتك في تحويل أفكارك إلى واقع رقمي. تواصل معنا اليوم لبدء رحلتك التقنية
              </p>
            </motion.div>
          </div>
        </section>

        {/* Contact Info */}
        <section className="py-20 bg-white dark:bg-slate-900">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
              {contactInfo.map((info, index) => (
                <motion.div
                  key={info.title}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="text-center p-6 bg-gray-50 dark:bg-slate-800 rounded-2xl hover:shadow-lg transition-shadow duration-300"
                >
                  <div className={`inline-flex p-4 rounded-2xl bg-gradient-to-br ${info.color} text-white mb-4`}>
                    <info.icon className="h-8 w-8" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                    {info.title}
                  </h3>
                  {info.details.map((detail, detailIndex) => (
                    <p key={detailIndex} className="text-gray-600 dark:text-gray-300 text-sm mb-1">
                      {detail}
                    </p>
                  ))}
                </motion.div>
              ))}
            </div>

            {/* Contact Form */}
            <div className="grid lg:grid-cols-2 gap-12">
              <motion.div
                initial={{ opacity: 0, x: -30 }}
                whileInView={{ opacity: 1, x: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6 }}
              >
                <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-6">
                  أرسل لنا رسالة
                </h2>
                <form className="space-y-6">
                  <div className="grid md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        الاسم الكامل
                      </label>
                      <input
                        type="text"
                        className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-slate-800 text-gray-900 dark:text-white"
                        placeholder="أدخل اسمك الكامل"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        البريد الإلكتروني
                      </label>
                      <input
                        type="email"
                        className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-slate-800 text-gray-900 dark:text-white"
                        placeholder="أدخل بريدك الإلكتروني"
                      />
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      رقم الهاتف
                    </label>
                    <input
                      type="tel"
                      className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-slate-800 text-gray-900 dark:text-white"
                      placeholder="أدخل رقم هاتفك"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      نوع المشروع
                    </label>
                    <select className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-slate-800 text-gray-900 dark:text-white">
                      <option>اختر نوع المشروع</option>
                      <option>تطبيق هاتف</option>
                      <option>موقع ويب</option>
                      <option>تطبيق حاسوب</option>
                      <option>استشارة تقنية</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      تفاصيل المشروع
                    </label>
                    <textarea
                      rows={6}
                      className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-slate-800 text-gray-900 dark:text-white"
                      placeholder="اكتب تفاصيل مشروعك هنا..."
                    />
                  </div>
                  <button
                    type="submit"
                    className="w-full btn-primary px-8 py-4 text-white font-bold rounded-xl text-lg"
                  >
                    إرسال الرسالة
                  </button>
                </form>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: 30 }}
                whileInView={{ opacity: 1, x: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6 }}
                className="bg-gradient-to-br from-blue-600 to-purple-700 rounded-2xl p-8 text-white"
              >
                <h3 className="text-2xl font-bold mb-6">لماذا تختار Codnet؟</h3>
                <div className="space-y-4">
                  {[
                    'استشارة مجانية لمدة 30 دقيقة',
                    'فريق خبراء متخصص',
                    'ضمان الجودة والتسليم في الوقت المحدد',
                    'دعم فني مستمر',
                    'أسعار تنافسية',
                    'تقنيات حديثة ومتطورة'
                  ].map((benefit, index) => (
                    <div key={index} className="flex items-center">
                      <div className="w-2 h-2 rounded-full bg-yellow-400 ml-3 flex-shrink-0" />
                      <span>{benefit}</span>
                    </div>
                  ))}
                </div>
                
                <div className="mt-8 p-4 bg-white/10 rounded-xl">
                  <h4 className="font-semibold mb-2">اتصل بنا الآن</h4>
                  <p className="text-sm opacity-90">
                    للحصول على استشارة فورية أو لمناقشة مشروعك
                  </p>
                  <a href="tel:+966501234567" className="inline-block mt-3 text-yellow-400 font-semibold">
                    +966 50 123 4567
                  </a>
                </div>
              </motion.div>
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </div>
  );
}
