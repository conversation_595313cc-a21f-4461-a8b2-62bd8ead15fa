{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/My%20Company%20Website/codnet-website/src/components/layout/Header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/layout/Header.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/Header.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAoS,GACjU,kEACA", "debugId": null}}, {"offset": {"line": 38, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/My%20Company%20Website/codnet-website/src/components/layout/Header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/layout/Header.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/Header.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAgR,GAC7S,8CACA", "debugId": null}}, {"offset": {"line": 52, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 62, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/My%20Company%20Website/codnet-website/src/components/ui/Logo.tsx"], "sourcesContent": ["import Link from 'next/link';\n\ninterface LogoProps {\n  className?: string;\n  size?: 'sm' | 'md' | 'lg';\n}\n\nexport default function Logo({ className = '', size = 'md' }: LogoProps) {\n  const sizeClasses = {\n    sm: 'h-6 w-6 text-sm',\n    md: 'h-8 w-8 text-lg',\n    lg: 'h-12 w-12 text-2xl'\n  };\n\n  const textSizeClasses = {\n    sm: 'text-lg',\n    md: 'text-xl',\n    lg: 'text-3xl'\n  };\n\n  return (\n    <Link href=\"/\" className={`flex items-center space-x-2 rtl:space-x-reverse ${className}`}>\n      <div className={`${sizeClasses[size]} rounded-xl bg-gradient-to-br from-blue-600 via-blue-700 to-amber-500 flex items-center justify-center shadow-lg pulse-glow`}>\n        <span className={`text-white font-bold ${textSizeClasses[size]}`}>C</span>\n      </div>\n      <span className={`${textSizeClasses[size]} font-bold gradient-text`}>Codnet</span>\n    </Link>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAOe,SAAS,KAAK,EAAE,YAAY,EAAE,EAAE,OAAO,IAAI,EAAa;IACrE,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,kBAAkB;QACtB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC,4JAAA,CAAA,UAAI;QAAC,MAAK;QAAI,WAAW,CAAC,gDAAgD,EAAE,WAAW;;0BACtF,8OAAC;gBAAI,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,2HAA2H,CAAC;0BAC/J,cAAA,8OAAC;oBAAK,WAAW,CAAC,qBAAqB,EAAE,eAAe,CAAC,KAAK,EAAE;8BAAE;;;;;;;;;;;0BAEpE,8OAAC;gBAAK,WAAW,GAAG,eAAe,CAAC,KAAK,CAAC,wBAAwB,CAAC;0BAAE;;;;;;;;;;;;AAG3E", "debugId": null}}, {"offset": {"line": 120, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/My%20Company%20Website/codnet-website/src/components/layout/Footer.tsx"], "sourcesContent": ["import Link from 'next/link';\nimport {\n  MapPinIcon,\n  PhoneIcon,\n  EnvelopeIcon,\n  ArrowTopRightOnSquareIcon\n} from '@heroicons/react/24/outline';\nimport Logo from '@/components/ui/Logo';\n\nconst navigation = {\n  services: [\n    { name: 'تطبيقات الهاتف', href: '/services/mobile' },\n    { name: 'تطبيقات الويب', href: '/services/web' },\n    { name: 'تطبيقات الحاسوب', href: '/services/desktop' },\n    { name: 'استشارات تقنية', href: '/services/consulting' },\n  ],\n  company: [\n    { name: 'من نحن', href: '/about' },\n    { name: 'معرض الأعمال', href: '/portfolio' },\n    { name: 'المدونة', href: '/blog' },\n    { name: 'الوظائف', href: '/careers' },\n  ],\n  support: [\n    { name: 'تواصل معنا', href: '/contact' },\n    { name: 'الأسئلة الشائعة', href: '/faq' },\n    { name: 'الدعم التقني', href: '/support' },\n    { name: 'سياسة الخصوصية', href: '/privacy' },\n  ],\n  social: [\n    {\n      name: 'LinkedIn',\n      href: '#',\n      icon: (props: any) => (\n        <svg fill=\"currentColor\" viewBox=\"0 0 24 24\" {...props}>\n          <path d=\"M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z\"/>\n        </svg>\n      ),\n    },\n    {\n      name: 'Twitter',\n      href: '#',\n      icon: (props: any) => (\n        <svg fill=\"currentColor\" viewBox=\"0 0 24 24\" {...props}>\n          <path d=\"M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84\"/>\n        </svg>\n      ),\n    },\n    {\n      name: 'GitHub',\n      href: '#',\n      icon: (props: any) => (\n        <svg fill=\"currentColor\" viewBox=\"0 0 24 24\" {...props}>\n          <path d=\"M12 2C6.477 2 2 6.484 2 12.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0112 6.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.202 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.943.359.309.678.92.678 1.855 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0022 12.017C22 6.484 17.522 2 12 2z\"/>\n        </svg>\n      ),\n    },\n  ],\n};\n\nexport default function Footer() {\n  return (\n    <footer className=\"bg-gray-900 text-white\">\n      <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n        {/* Main footer content */}\n        <div className=\"py-16\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n            {/* Company info */}\n            <div className=\"lg:col-span-1\">\n              <div className=\"mb-4\">\n                <Logo className=\"text-white\" />\n              </div>\n              <p className=\"text-gray-300 mb-6 leading-relaxed\">\n                نصمم المستقبل الرقمي لعملك. شركة رائدة في تطوير البرمجيات والحلول التقنية المبتكرة.\n              </p>\n              \n              {/* Contact info */}\n              <div className=\"space-y-3\">\n                <div className=\"flex items-center space-x-3 rtl:space-x-reverse\">\n                  <PhoneIcon className=\"h-5 w-5 text-blue-400\" />\n                  <span className=\"text-gray-300\">+966 50 123 4567</span>\n                </div>\n                <div className=\"flex items-center space-x-3 rtl:space-x-reverse\">\n                  <EnvelopeIcon className=\"h-5 w-5 text-blue-400\" />\n                  <span className=\"text-gray-300\"><EMAIL></span>\n                </div>\n                <div className=\"flex items-center space-x-3 rtl:space-x-reverse\">\n                  <MapPinIcon className=\"h-5 w-5 text-blue-400\" />\n                  <span className=\"text-gray-300\">الرياض، المملكة العربية السعودية</span>\n                </div>\n              </div>\n            </div>\n\n            {/* Services */}\n            <div>\n              <h3 className=\"text-lg font-semibold mb-4\">خدماتنا</h3>\n              <ul className=\"space-y-3\">\n                {navigation.services.map((item) => (\n                  <li key={item.name}>\n                    <Link\n                      href={item.href}\n                      className=\"text-gray-300 hover:text-white transition-colors duration-200 flex items-center group\"\n                    >\n                      {item.name}\n                      <ArrowTopRightOnSquareIcon className=\"h-4 w-4 mr-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200\" />\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </div>\n\n            {/* Company */}\n            <div>\n              <h3 className=\"text-lg font-semibold mb-4\">الشركة</h3>\n              <ul className=\"space-y-3\">\n                {navigation.company.map((item) => (\n                  <li key={item.name}>\n                    <Link\n                      href={item.href}\n                      className=\"text-gray-300 hover:text-white transition-colors duration-200 flex items-center group\"\n                    >\n                      {item.name}\n                      <ArrowTopRightOnSquareIcon className=\"h-4 w-4 mr-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200\" />\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </div>\n\n            {/* Support */}\n            <div>\n              <h3 className=\"text-lg font-semibold mb-4\">الدعم</h3>\n              <ul className=\"space-y-3\">\n                {navigation.support.map((item) => (\n                  <li key={item.name}>\n                    <Link\n                      href={item.href}\n                      className=\"text-gray-300 hover:text-white transition-colors duration-200 flex items-center group\"\n                    >\n                      {item.name}\n                      <ArrowTopRightOnSquareIcon className=\"h-4 w-4 mr-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200\" />\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </div>\n          </div>\n        </div>\n\n        {/* Newsletter signup */}\n        <div className=\"border-t border-gray-800 py-8\">\n          <div className=\"flex flex-col md:flex-row items-center justify-between\">\n            <div className=\"mb-4 md:mb-0\">\n              <h3 className=\"text-lg font-semibold mb-2\">اشترك في نشرتنا الإخبارية</h3>\n              <p className=\"text-gray-300\">احصل على آخر الأخبار والتحديثات التقنية</p>\n            </div>\n            <div className=\"flex w-full md:w-auto\">\n              <input\n                type=\"email\"\n                placeholder=\"أدخل بريدك الإلكتروني\"\n                className=\"flex-1 md:w-64 px-4 py-2 bg-gray-800 border border-gray-700 rounded-r-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-white placeholder-gray-400\"\n              />\n              <button className=\"btn-primary px-6 py-2 text-white font-medium rounded-l-lg\">\n                اشتراك\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Bottom footer */}\n        <div className=\"border-t border-gray-800 py-6\">\n          <div className=\"flex flex-col md:flex-row items-center justify-between\">\n            <p className=\"text-gray-400 text-sm mb-4 md:mb-0\">\n              © 2024 Codnet. جميع الحقوق محفوظة.\n            </p>\n            \n            {/* Social links */}\n            <div className=\"flex space-x-6 rtl:space-x-reverse\">\n              {navigation.social.map((item) => (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className=\"text-gray-400 hover:text-white transition-colors duration-200\"\n                >\n                  <span className=\"sr-only\">{item.name}</span>\n                  <item.icon className=\"h-6 w-6\" />\n                </Link>\n              ))}\n            </div>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;AAAA;AAMA;;;;;AAEA,MAAM,aAAa;IACjB,UAAU;QACR;YAAE,MAAM;YAAkB,MAAM;QAAmB;QACnD;YAAE,MAAM;YAAiB,MAAM;QAAgB;QAC/C;YAAE,MAAM;YAAmB,MAAM;QAAoB;QACrD;YAAE,MAAM;YAAkB,MAAM;QAAuB;KACxD;IACD,SAAS;QACP;YAAE,MAAM;YAAU,MAAM;QAAS;QACjC;YAAE,MAAM;YAAgB,MAAM;QAAa;QAC3C;YAAE,MAAM;YAAW,MAAM;QAAQ;QACjC;YAAE,MAAM;YAAW,MAAM;QAAW;KACrC;IACD,SAAS;QACP;YAAE,MAAM;YAAc,MAAM;QAAW;QACvC;YAAE,MAAM;YAAmB,MAAM;QAAO;QACxC;YAAE,MAAM;YAAgB,MAAM;QAAW;QACzC;YAAE,MAAM;YAAkB,MAAM;QAAW;KAC5C;IACD,QAAQ;QACN;YACE,MAAM;YACN,MAAM;YACN,MAAM,CAAC,sBACL,8OAAC;oBAAI,MAAK;oBAAe,SAAQ;oBAAa,GAAG,KAAK;8BACpD,cAAA,8OAAC;wBAAK,GAAE;;;;;;;;;;;QAGd;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,CAAC,sBACL,8OAAC;oBAAI,MAAK;oBAAe,SAAQ;oBAAa,GAAG,KAAK;8BACpD,cAAA,8OAAC;wBAAK,GAAE;;;;;;;;;;;QAGd;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,CAAC,sBACL,8OAAC;oBAAI,MAAK;oBAAe,SAAQ;oBAAa,GAAG,KAAK;8BACpD,cAAA,8OAAC;wBAAK,GAAE;;;;;;;;;;;QAGd;KACD;AACH;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,gIAAA,CAAA,UAAI;4CAAC,WAAU;;;;;;;;;;;kDAElB,8OAAC;wCAAE,WAAU;kDAAqC;;;;;;kDAKlD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iNAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;kEACrB,8OAAC;wDAAK,WAAU;kEAAgB;;;;;;;;;;;;0DAElC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,uNAAA,CAAA,eAAY;wDAAC,WAAU;;;;;;kEACxB,8OAAC;wDAAK,WAAU;kEAAgB;;;;;;;;;;;;0DAElC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,mNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;kEACtB,8OAAC;wDAAK,WAAU;kEAAgB;;;;;;;;;;;;;;;;;;;;;;;;0CAMtC,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,8OAAC;wCAAG,WAAU;kDACX,WAAW,QAAQ,CAAC,GAAG,CAAC,CAAC,qBACxB,8OAAC;0DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAU;;wDAET,KAAK,IAAI;sEACV,8OAAC,iPAAA,CAAA,4BAAyB;4DAAC,WAAU;;;;;;;;;;;;+CANhC,KAAK,IAAI;;;;;;;;;;;;;;;;0CAcxB,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,8OAAC;wCAAG,WAAU;kDACX,WAAW,OAAO,CAAC,GAAG,CAAC,CAAC,qBACvB,8OAAC;0DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAU;;wDAET,KAAK,IAAI;sEACV,8OAAC,iPAAA,CAAA,4BAAyB;4DAAC,WAAU;;;;;;;;;;;;+CANhC,KAAK,IAAI;;;;;;;;;;;;;;;;0CAcxB,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,8OAAC;wCAAG,WAAU;kDACX,WAAW,OAAO,CAAC,GAAG,CAAC,CAAC,qBACvB,8OAAC;0DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAU;;wDAET,KAAK,IAAI;sEACV,8OAAC,iPAAA,CAAA,4BAAyB;4DAAC,WAAU;;;;;;;;;;;;+CANhC,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAgB5B,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,8OAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;0CAE/B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,WAAU;;;;;;kDAEZ,8OAAC;wCAAO,WAAU;kDAA4D;;;;;;;;;;;;;;;;;;;;;;;8BAQpF,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;0CAAqC;;;;;;0CAKlD,8OAAC;gCAAI,WAAU;0CACZ,WAAW,MAAM,CAAC,GAAG,CAAC,CAAC,qBACtB,8OAAC,4JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAU;;0DAEV,8OAAC;gDAAK,WAAU;0DAAW,KAAK,IAAI;;;;;;0DACpC,8OAAC,KAAK,IAAI;gDAAC,WAAU;;;;;;;uCALhB,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAchC", "debugId": null}}, {"offset": {"line": 668, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/My%20Company%20Website/codnet-website/src/components/sections/HeroSection.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/sections/HeroSection.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/sections/HeroSection.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA2S,GACxU,yEACA", "debugId": null}}, {"offset": {"line": 682, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/My%20Company%20Website/codnet-website/src/components/sections/HeroSection.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/sections/HeroSection.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/sections/HeroSection.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAuR,GACpT,qDACA", "debugId": null}}, {"offset": {"line": 696, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 706, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/My%20Company%20Website/codnet-website/src/components/sections/ServicesSection.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/sections/ServicesSection.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/sections/ServicesSection.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA+S,GAC5U,6EACA", "debugId": null}}, {"offset": {"line": 720, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/My%20Company%20Website/codnet-website/src/components/sections/ServicesSection.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/sections/ServicesSection.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/sections/ServicesSection.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA2R,GACxT,yDACA", "debugId": null}}, {"offset": {"line": 734, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 744, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/My%20Company%20Website/codnet-website/src/components/sections/PortfolioSection.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/sections/PortfolioSection.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/sections/PortfolioSection.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAgT,GAC7U,8EACA", "debugId": null}}, {"offset": {"line": 758, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/My%20Company%20Website/codnet-website/src/components/sections/PortfolioSection.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/sections/PortfolioSection.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/sections/PortfolioSection.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA4R,GACzT,0DACA", "debugId": null}}, {"offset": {"line": 772, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 782, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/My%20Company%20Website/codnet-website/src/components/sections/TestimonialsSection.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/sections/TestimonialsSection.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/sections/TestimonialsSection.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAmT,GAChV,iFACA", "debugId": null}}, {"offset": {"line": 796, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/My%20Company%20Website/codnet-website/src/components/sections/TestimonialsSection.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/sections/TestimonialsSection.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/sections/TestimonialsSection.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA+R,GAC5T,6DACA", "debugId": null}}, {"offset": {"line": 810, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 820, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/My%20Company%20Website/codnet-website/src/components/sections/WhyChooseUsSection.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/sections/WhyChooseUsSection.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/sections/WhyChooseUsSection.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAkT,GAC/U,gFACA", "debugId": null}}, {"offset": {"line": 834, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/My%20Company%20Website/codnet-website/src/components/sections/WhyChooseUsSection.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/sections/WhyChooseUsSection.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/sections/WhyChooseUsSection.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA8R,GAC3T,4DACA", "debugId": null}}, {"offset": {"line": 848, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 858, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/My%20Company%20Website/codnet-website/src/components/sections/CTASection.tsx"], "sourcesContent": ["import Link from 'next/link';\nimport { motion } from 'framer-motion';\nimport { ArrowLeftIcon } from '@heroicons/react/24/outline';\n\nexport default function CTASection() {\n  return (\n    <section className=\"py-20 bg-gradient-to-br from-blue-900 via-indigo-900 to-purple-900 text-white relative overflow-hidden\">\n      {/* Background Elements */}\n      <div className=\"absolute inset-0\">\n        <motion.div\n          className=\"absolute top-20 left-10 w-72 h-72 bg-blue-400/20 rounded-full blur-3xl\"\n          animate={{\n            x: [0, 100, 0],\n            y: [0, -50, 0],\n          }}\n          transition={{\n            duration: 20,\n            repeat: Infinity,\n            ease: \"linear\"\n          }}\n        />\n        <motion.div\n          className=\"absolute bottom-20 right-10 w-96 h-96 bg-purple-400/20 rounded-full blur-3xl\"\n          animate={{\n            x: [0, -80, 0],\n            y: [0, 60, 0],\n          }}\n          transition={{\n            duration: 25,\n            repeat: Infinity,\n            ease: \"linear\"\n          }}\n        />\n      </div>\n\n      <div className=\"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          viewport={{ once: true }}\n          transition={{ duration: 0.6 }}\n        >\n          <h2 className=\"text-3xl md:text-4xl lg:text-5xl font-bold mb-6 leading-tight\">\n            هل أنت مستعد لتحويل{' '}\n            <span className=\"bg-gradient-to-r from-yellow-400 to-orange-400 bg-clip-text text-transparent\">\n              فكرتك\n            </span>\n            {' '}إلى واقع؟\n          </h2>\n          <p className=\"text-xl mb-8 text-blue-100 max-w-3xl mx-auto\">\n            انضم إلى أكثر من 50 عميل راضي واكتشف كيف يمكن لحلولنا التقنية المبتكرة أن تدفع نمو عملك إلى آفاق جديدة\n          </p>\n\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center items-center mb-8\">\n            <Link\n              href=\"/contact\"\n              className=\"btn-secondary inline-flex items-center px-10 py-5 text-white font-bold rounded-xl text-xl shadow-2xl\"\n            >\n              احصل على استشارة مجانية\n              <ArrowLeftIcon className=\"mr-2 h-6 w-6\" />\n            </Link>\n\n            <div className=\"flex items-center space-x-6 rtl:space-x-reverse text-blue-200 text-sm\">\n              <div className=\"flex items-center\">\n                <span className=\"text-yellow-400 ml-1\">⭐</span>\n                تقييم 5/5 نجوم\n              </div>\n              <div className=\"flex items-center\">\n                <span className=\"text-green-400 ml-1\">✓</span>\n                ضمان الجودة\n              </div>\n            </div>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto\">\n            {[\n              { icon: '📞', title: 'اتصل بنا', desc: '+966 50 123 4567' },\n              { icon: '✉️', title: 'راسلنا', desc: '<EMAIL>' },\n              { icon: '📍', title: 'زورنا', desc: 'الرياض، السعودية' }\n            ].map((contact, index) => (\n              <motion.div\n                key={contact.title}\n                initial={{ opacity: 0, y: 30 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                viewport={{ once: true }}\n                transition={{ duration: 0.6, delay: 0.2 + index * 0.1 }}\n                className=\"p-4 bg-white/10 backdrop-blur-sm rounded-xl border border-white/20\"\n              >\n                <div className=\"text-2xl mb-2\">{contact.icon}</div>\n                <h3 className=\"font-semibold mb-1\">{contact.title}</h3>\n                <p className=\"text-blue-200 text-sm\">{contact.desc}</p>\n              </motion.div>\n            ))}\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,wJAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BACP,GAAG;gCAAC;gCAAG;gCAAK;6BAAE;4BACd,GAAG;gCAAC;gCAAG,CAAC;gCAAI;6BAAE;wBAChB;wBACA,YAAY;4BACV,UAAU;4BACV,QAAQ;4BACR,MAAM;wBACR;;;;;;kCAEF,8OAAC,wJAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BACP,GAAG;gCAAC;gCAAG,CAAC;gCAAI;6BAAE;4BACd,GAAG;gCAAC;gCAAG;gCAAI;6BAAE;wBACf;wBACA,YAAY;4BACV,UAAU;4BACV,QAAQ;4BACR,MAAM;wBACR;;;;;;;;;;;;0BAIJ,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,wJAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,YAAY;wBAAE,UAAU;oBAAI;;sCAE5B,8OAAC;4BAAG,WAAU;;gCAAgE;gCACxD;8CACpB,8OAAC;oCAAK,WAAU;8CAA+E;;;;;;gCAG9F;gCAAI;;;;;;;sCAEP,8OAAC;4BAAE,WAAU;sCAA+C;;;;;;sCAI5D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;;wCACX;sDAEC,8OAAC,yNAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;;;;;;;8CAG3B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAuB;;;;;;gDAAQ;;;;;;;sDAGjD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAsB;;;;;;gDAAQ;;;;;;;;;;;;;;;;;;;sCAMpD,8OAAC;4BAAI,WAAU;sCACZ;gCACC;oCAAE,MAAM;oCAAM,OAAO;oCAAY,MAAM;gCAAmB;gCAC1D;oCAAE,MAAM;oCAAM,OAAO;oCAAU,MAAM;gCAAkB;gCACvD;oCAAE,MAAM;oCAAM,OAAO;oCAAS,MAAM;gCAAmB;6BACxD,CAAC,GAAG,CAAC,CAAC,SAAS,sBACd,8OAAC,wJAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,UAAU;wCAAE,MAAM;oCAAK;oCACvB,YAAY;wCAAE,UAAU;wCAAK,OAAO,MAAM,QAAQ;oCAAI;oCACtD,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;sDAAiB,QAAQ,IAAI;;;;;;sDAC5C,8OAAC;4CAAG,WAAU;sDAAsB,QAAQ,KAAK;;;;;;sDACjD,8OAAC;4CAAE,WAAU;sDAAyB,QAAQ,IAAI;;;;;;;mCAT7C,QAAQ,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiBlC", "debugId": null}}, {"offset": {"line": 1144, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/My%20Company%20Website/codnet-website/src/app/page.tsx"], "sourcesContent": ["import Header from '@/components/layout/Header';\nimport Footer from '@/components/layout/Footer';\nimport HeroSection from '@/components/sections/HeroSection';\nimport ServicesSection from '@/components/sections/ServicesSection';\nimport PortfolioSection from '@/components/sections/PortfolioSection';\nimport TestimonialsSection from '@/components/sections/TestimonialsSection';\nimport WhyChooseUsSection from '@/components/sections/WhyChooseUsSection';\nimport CTASection from '@/components/sections/CTASection';\n\nexport default function Home() {\n  return (\n    <div className=\"min-h-screen\">\n      <Header />\n      <main>\n        <HeroSection />\n        <ServicesSection />\n        <PortfolioSection />\n        <WhyChooseUsSection />\n        <TestimonialsSection />\n        <CTASection />\n      </main>\n      <Footer />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,sIAAA,CAAA,UAAM;;;;;0BACP,8OAAC;;kCACC,8OAAC,6IAAA,CAAA,UAAW;;;;;kCACZ,8OAAC,iJAAA,CAAA,UAAe;;;;;kCAChB,8OAAC,kJAAA,CAAA,UAAgB;;;;;kCACjB,8OAAC,oJAAA,CAAA,UAAkB;;;;;kCACnB,8OAAC,qJAAA,CAAA,UAAmB;;;;;kCACpB,8OAAC,4IAAA,CAAA,UAAU;;;;;;;;;;;0BAEb,8OAAC,sIAAA,CAAA,UAAM;;;;;;;;;;;AAGb", "debugId": null}}]}