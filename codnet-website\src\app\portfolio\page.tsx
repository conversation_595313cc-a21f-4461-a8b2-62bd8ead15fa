'use client';

import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import { motion } from 'framer-motion';
import { useState } from 'react';
import { ArrowTopRightOnSquareIcon, EyeIcon } from '@heroicons/react/24/outline';

const categories = ['الكل', 'تطبيقات الهاتف', 'مواقع الويب', 'تطبيقات الحاسوب'];

const projects = [
  {
    id: 1,
    title: 'تطبيق التجارة الإلكترونية',
    category: 'تطبيقات الهاتف',
    description: 'تطبيق متكامل للتسوق الإلكتروني مع نظام دفع آمن وتتبع الطلبات',
    image: '/api/placeholder/400/300',
    technologies: ['React Native', 'Node.js', 'MongoDB'],
    client: 'شركة التجارة الذكية',
    year: '2024',
    results: ['زيادة المبيعات 150%', 'تحسين تجربة المستخدم', '50K+ تحميل'],
    href: '/portfolio/ecommerce-app'
  },
  {
    id: 2,
    title: 'منصة التعلم الإلكتروني',
    category: 'مواقع الويب',
    description: 'منصة تعليمية تفاعلية مع نظام إدارة الدورات والاختبارات',
    image: '/api/placeholder/400/300',
    technologies: ['Next.js', 'PostgreSQL', 'WebRTC'],
    client: 'معهد التقنية المتقدمة',
    year: '2024',
    results: ['10K+ طالب مسجل', 'تقييم 4.8/5', 'نمو 200%'],
    href: '/portfolio/learning-platform'
  },
  {
    id: 3,
    title: 'نظام إدارة المخزون',
    category: 'تطبيقات الحاسوب',
    description: 'برنامج شامل لإدارة المخزون والمبيعات مع تقارير تحليلية',
    image: '/api/placeholder/400/300',
    technologies: ['Electron', 'React', 'SQLite'],
    client: 'مجموعة الأعمال المتحدة',
    year: '2023',
    results: ['توفير 40% من الوقت', 'دقة 99.5%', 'ROI 300%'],
    href: '/portfolio/inventory-system'
  },
  {
    id: 4,
    title: 'تطبيق الصحة واللياقة',
    category: 'تطبيقات الهاتف',
    description: 'تطبيق لتتبع اللياقة البدنية والصحة مع مدرب شخصي ذكي',
    image: '/api/placeholder/400/300',
    technologies: ['Flutter', 'Firebase', 'AI/ML'],
    client: 'مركز اللياقة الذكي',
    year: '2024',
    results: ['100K+ مستخدم نشط', 'تقييم 4.9/5', 'نمو 180%'],
    href: '/portfolio/fitness-app'
  },
  {
    id: 5,
    title: 'موقع الشركة المؤسسية',
    category: 'مواقع الويب',
    description: 'موقع مؤسسي متطور مع نظام إدارة المحتوى ومتعدد اللغات',
    image: '/api/placeholder/400/300',
    technologies: ['Next.js', 'Strapi', 'Tailwind'],
    client: 'الشركة العالمية للاستثمار',
    year: '2023',
    results: ['زيادة الزيارات 250%', 'تحسين SEO', 'تقليل معدل الارتداد 60%'],
    href: '/portfolio/corporate-website'
  },
  {
    id: 6,
    title: 'برنامج المحاسبة المتقدم',
    category: 'تطبيقات الحاسوب',
    description: 'نظام محاسبي شامل مع تقارير مالية وضريبية متقدمة',
    image: '/api/placeholder/400/300',
    technologies: ['C#', '.NET', 'SQL Server'],
    client: 'مكتب المحاسبة المتخصص',
    year: '2023',
    results: ['أتمتة 90% من العمليات', 'دقة 99.9%', 'توفير 50% من الوقت'],
    href: '/portfolio/accounting-software'
  }
];

export default function PortfolioPage() {
  const [activeCategory, setActiveCategory] = useState('الكل');
  const [hoveredProject, setHoveredProject] = useState<number | null>(null);

  const filteredProjects = activeCategory === 'الكل' 
    ? projects 
    : projects.filter(project => project.category === activeCategory);

  return (
    <div className="min-h-screen">
      <Header />
      <main className="pt-20">
        {/* Hero Section */}
        <section className="py-20 bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-slate-900 dark:via-blue-900 dark:to-indigo-900">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-6">
                معرض أعمالنا
              </h1>
              <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto mb-8">
                اكتشف مجموعة من مشاريعنا الناجحة التي حققت نتائج استثنائية لعملائنا
              </p>

              {/* Category Filter */}
              <div className="flex flex-wrap justify-center gap-4">
                {categories.map((category) => (
                  <button
                    key={category}
                    onClick={() => setActiveCategory(category)}
                    className={`px-6 py-3 rounded-full font-medium transition-all duration-300 ${
                      activeCategory === category
                        ? 'bg-blue-600 text-white shadow-lg'
                        : 'bg-white dark:bg-slate-700 text-gray-700 dark:text-gray-300 hover:bg-blue-50 dark:hover:bg-slate-600'
                    }`}
                  >
                    {category}
                  </button>
                ))}
              </div>
            </motion.div>
          </div>
        </section>

        {/* Projects Grid */}
        <section className="py-20 bg-white dark:bg-slate-900">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              layout
              className="grid md:grid-cols-2 lg:grid-cols-3 gap-8"
            >
              {filteredProjects.map((project, index) => (
                <motion.div
                  key={project.id}
                  layout
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.9 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  className="group relative bg-white dark:bg-slate-800 rounded-2xl overflow-hidden shadow-lg card-hover"
                  onMouseEnter={() => setHoveredProject(project.id)}
                  onMouseLeave={() => setHoveredProject(null)}
                >
                  {/* Project Image */}
                  <div className="relative h-48 bg-gradient-to-br from-blue-500 to-purple-600 overflow-hidden">
                    {/* Placeholder for project image */}
                    <div className="absolute inset-0 bg-gradient-to-br from-blue-500/20 to-purple-600/20" />
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="text-white text-6xl opacity-20">📱</div>
                    </div>
                    
                    {/* Overlay */}
                    <div className={`absolute inset-0 bg-black/50 transition-opacity duration-300 ${
                      hoveredProject === project.id ? 'opacity-100' : 'opacity-0'
                    }`}>
                      <div className="absolute inset-0 flex items-center justify-center">
                        <a
                          href={project.href}
                          className="inline-flex items-center px-4 py-2 bg-white text-gray-900 rounded-lg font-medium hover:bg-gray-100 transition-colors duration-200"
                        >
                          <EyeIcon className="h-5 w-5 ml-2" />
                          عرض المشروع
                        </a>
                      </div>
                    </div>

                    {/* Category Badge */}
                    <div className="absolute top-4 right-4">
                      <span className="px-3 py-1 bg-white/90 text-gray-900 text-xs font-medium rounded-full">
                        {project.category}
                      </span>
                    </div>
                  </div>

                  {/* Project Content */}
                  <div className="p-6">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="text-xl font-bold text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-300">
                        {project.title}
                      </h3>
                      <span className="text-sm text-gray-500 dark:text-gray-400">
                        {project.year}
                      </span>
                    </div>

                    <p className="text-gray-600 dark:text-gray-300 mb-4 leading-relaxed">
                      {project.description}
                    </p>

                    {/* Client */}
                    <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">
                      العميل: {project.client}
                    </p>

                    {/* Technologies */}
                    <div className="flex flex-wrap gap-2 mb-4">
                      {project.technologies.map((tech, techIndex) => (
                        <span
                          key={techIndex}
                          className="px-2 py-1 text-xs font-medium bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200 rounded-md"
                        >
                          {tech}
                        </span>
                      ))}
                    </div>

                    {/* Results */}
                    <div className="space-y-1 mb-4">
                      {project.results.slice(0, 2).map((result, resultIndex) => (
                        <div key={resultIndex} className="flex items-center text-sm text-gray-600 dark:text-gray-300">
                          <div className="w-1.5 h-1.5 rounded-full bg-green-500 ml-2 flex-shrink-0" />
                          {result}
                        </div>
                      ))}
                    </div>

                    {/* CTA */}
                    <a
                      href={project.href}
                      className="inline-flex items-center text-sm font-semibold text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 transition-colors duration-300"
                    >
                      اقرأ دراسة الحالة
                      <ArrowTopRightOnSquareIcon className="mr-1 h-4 w-4 group-hover:-translate-y-0.5 group-hover:translate-x-0.5 transition-transform duration-300" />
                    </a>
                  </div>
                </motion.div>
              ))}
            </motion.div>
          </div>
        </section>
      </main>
      <Footer />
    </div>
  );
}
