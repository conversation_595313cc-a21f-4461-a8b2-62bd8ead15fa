'use client';

import { motion } from 'framer-motion';
import { 
  LightBulbIcon,
  ShieldCheckIcon,
  ClockIcon,
  UserGroupIcon,
  CogIcon,
  TrophyIcon,
  HeartIcon,
  RocketLaunchIcon
} from '@heroicons/react/24/outline';

const features = [
  {
    icon: LightBulbIcon,
    title: 'الابتكار والإبداع',
    description: 'نستخدم أحدث التقنيات والأساليب المبتكرة لتطوير حلول فريدة تميز عملك عن المنافسين',
    color: 'from-yellow-500 to-orange-500'
  },
  {
    icon: ShieldCheckIcon,
    title: 'الجودة والموثوقية',
    description: 'نلتزم بأعلى معايير الجودة في كل مرحلة من مراحل التطوير مع ضمان الأمان والاستقرار',
    color: 'from-green-500 to-emerald-500'
  },
  {
    icon: ClockIcon,
    title: 'التسليم في الوقت المحدد',
    description: 'نحترم المواعيد المتفق عليها ونضمن تسليم مشروعك في الوقت المحدد دون التنازل عن الجودة',
    color: 'from-blue-500 to-cyan-500'
  },
  {
    icon: UserGroupIcon,
    title: 'فريق خبراء متخصص',
    description: 'فريقنا من المطورين والمصممين ذوي الخبرة العالية يضمن تنفيذ مشروعك بأفضل الممارسات',
    color: 'from-purple-500 to-pink-500'
  },
  {
    icon: CogIcon,
    title: 'دعم فني مستمر',
    description: 'نقدم دعماً فنياً شاملاً ومستمراً حتى بعد تسليم المشروع لضمان استمرارية العمل بكفاءة',
    color: 'from-red-500 to-rose-500'
  },
  {
    icon: TrophyIcon,
    title: 'سجل حافل بالنجاحات',
    description: 'أكثر من 100 مشروع ناجح و50+ عميل راضي يشهدون على جودة خدماتنا وتميز أدائنا',
    color: 'from-amber-500 to-yellow-500'
  },
  {
    icon: HeartIcon,
    title: 'شراكة طويلة المدى',
    description: 'نؤمن ببناء علاقات شراكة قوية مع عملائنا تتجاوز حدود المشروع الواحد',
    color: 'from-pink-500 to-red-500'
  },
  {
    icon: RocketLaunchIcon,
    title: 'نمو وتطوير مستمر',
    description: 'نساعد عملاءنا على النمو والتطور من خلال حلول قابلة للتوسع ومواكبة للتطورات التقنية',
    color: 'from-indigo-500 to-purple-500'
  }
];

const stats = [
  { number: '100+', label: 'مشروع مكتمل', icon: '🚀' },
  { number: '50+', label: 'عميل راضي', icon: '😊' },
  { number: '5+', label: 'سنوات خبرة', icon: '⭐' },
  { number: '99%', label: 'معدل رضا العملاء', icon: '💯' }
];

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1
    }
  }
};

const itemVariants = {
  hidden: { opacity: 0, y: 30 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.6,
      ease: "easeOut"
    }
  }
};

export default function WhyChooseUsSection() {
  return (
    <section className="py-20 bg-white dark:bg-slate-900 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-10 w-72 h-72 bg-blue-400/10 rounded-full blur-3xl" />
        <div className="absolute bottom-20 right-10 w-96 h-96 bg-purple-400/10 rounded-full blur-3xl" />
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 dark:text-white mb-4">
            لماذا تختار <span className="gradient-text">Codnet</span>؟
          </h2>
          <p className="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            نحن لا نطور مجرد تطبيقات، بل نبني شراكات نجاح طويلة المدى مع عملائنا
          </p>
        </motion.div>

        {/* Stats Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="grid grid-cols-2 md:grid-cols-4 gap-8 mb-20"
        >
          {stats.map((stat, index) => (
            <motion.div
              key={stat.label}
              initial={{ opacity: 0, scale: 0.8 }}
              whileInView={{ opacity: 1, scale: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.3 + index * 0.1 }}
              className="text-center p-6 bg-gray-50 dark:bg-slate-800 rounded-2xl hover:shadow-lg transition-shadow duration-300"
            >
              <div className="text-4xl mb-2">{stat.icon}</div>
              <div className="text-2xl md:text-3xl font-bold text-blue-600 dark:text-blue-400 mb-1">
                {stat.number}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                {stat.label}
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Features Grid */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="grid md:grid-cols-2 lg:grid-cols-4 gap-8"
        >
          {features.map((feature, index) => (
            <motion.div
              key={feature.title}
              variants={itemVariants}
              className="group relative p-6 bg-gray-50 dark:bg-slate-800 rounded-2xl hover:bg-white dark:hover:bg-slate-700 card-hover"
            >
              {/* Icon */}
              <div className={`inline-flex p-4 rounded-2xl bg-gradient-to-br ${feature.color} text-white mb-4 group-hover:scale-110 transition-transform duration-300 shadow-lg floating-animation`}>
                <feature.icon className="h-6 w-6" />
              </div>

              {/* Content */}
              <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-3 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-300">
                {feature.title}
              </h3>
              <p className="text-gray-600 dark:text-gray-300 text-sm leading-relaxed">
                {feature.description}
              </p>

              {/* Hover effect */}
              <div className={`absolute inset-0 rounded-2xl bg-gradient-to-br ${feature.color} opacity-0 group-hover:opacity-5 transition-opacity duration-300`} />
            </motion.div>
          ))}
        </motion.div>

        {/* Process Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="mt-20 text-center"
        >
          <h3 className="text-2xl md:text-3xl font-bold text-gray-900 dark:text-white mb-8">
            منهجية العمل المتطورة
          </h3>
          
          <div className="grid md:grid-cols-4 gap-8">
            {[
              { step: '01', title: 'التحليل والتخطيط', description: 'دراسة شاملة لمتطلباتك وأهدافك' },
              { step: '02', title: 'التصميم والنمذجة', description: 'تصميم واجهات مستخدم جذابة وسهلة الاستخدام' },
              { step: '03', title: 'التطوير والبرمجة', description: 'تطوير الحل باستخدام أحدث التقنيات' },
              { step: '04', title: 'الاختبار والتسليم', description: 'اختبارات شاملة وتسليم المشروع مع الدعم' }
            ].map((process, index) => (
              <motion.div
                key={process.step}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: 0.5 + index * 0.1 }}
                className="relative"
              >
                <div className="text-4xl font-bold text-blue-600/20 dark:text-blue-400/20 mb-4">
                  {process.step}
                </div>
                <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  {process.title}
                </h4>
                <p className="text-gray-600 dark:text-gray-300 text-sm">
                  {process.description}
                </p>
                
                {/* Connector line */}
                {index < 3 && (
                  <div className="hidden md:block absolute top-8 left-full w-full h-0.5 bg-gradient-to-r from-blue-600/50 to-transparent" />
                )}
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  );
}
