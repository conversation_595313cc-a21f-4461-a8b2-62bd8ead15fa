@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: #ffffff;
  --foreground: #1a1a1a;
  --primary: #2563eb;
  --primary-dark: #1d4ed8;
  --secondary: #64748b;
  --accent: #f59e0b;
  --muted: #f8fafc;
  --border: #e2e8f0;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-primary: var(--primary);
  --color-secondary: var(--secondary);
  --color-accent: var(--accent);
  --color-muted: var(--muted);
  --color-border: var(--border);
  --font-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0f172a;
    --foreground: #ffffff;
    --primary: #3b82f6;
    --primary-dark: #2563eb;
    --secondary: #94a3b8;
    --accent: #fbbf24;
    --muted: #1e293b;
    --border: #334155;
  }
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html {
  scroll-behavior: smooth;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  .gradient-text {
    background: linear-gradient(135deg, #2563eb, #f59e0b);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    background-size: 200% 200%;
    animation: gradient-shift 3s ease-in-out infinite;
  }

  .glass-effect {
    backdrop-filter: blur(16px);
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  }

  .card-hover {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .card-hover:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  }

  .btn-primary {
    background: linear-gradient(135deg, #2563eb, #1d4ed8);
    box-shadow: 0 4px 15px rgba(37, 99, 235, 0.4);
    transition: all 0.3s ease;
  }

  .btn-primary:hover {
    background: linear-gradient(135deg, #1d4ed8, #1e40af);
    box-shadow: 0 8px 25px rgba(37, 99, 235, 0.6);
    transform: translateY(-2px);
  }

  .btn-secondary {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    box-shadow: 0 4px 15px rgba(245, 158, 11, 0.4);
    transition: all 0.3s ease;
  }

  .btn-secondary:hover {
    background: linear-gradient(135deg, #d97706, #b45309);
    box-shadow: 0 8px 25px rgba(245, 158, 11, 0.6);
    transform: translateY(-2px);
  }

  .floating-animation {
    animation: float 6s ease-in-out infinite;
  }

  .pulse-glow {
    animation: pulse-glow 2s ease-in-out infinite alternate;
  }

  .section-padding {
    padding: 5rem 0;
  }

  @media (max-width: 768px) {
    .section-padding {
      padding: 3rem 0;
    }
  }

  /* Page Layout Fixes */
  .page-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
  }

  .page-content {
    flex: 1;
    width: 100%;
    overflow-x: hidden;
  }

  .section-container {
    width: 100%;
    max-width: 100vw;
    overflow-x: hidden;
  }

  .content-wrapper {
    max-width: 1280px;
    margin: 0 auto;
    padding: 0 1rem;
  }

  @media (min-width: 640px) {
    .content-wrapper {
      padding: 0 1.5rem;
    }
  }

  @media (min-width: 1024px) {
    .content-wrapper {
      padding: 0 2rem;
    }
  }

  /* Form Styling */
  .form-input {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    background: #ffffff;
    color: #1f2937;
    font-size: 1rem;
    transition: all 0.2s ease;
  }

  .form-input:focus {
    outline: none;
    border-color: #2563eb;
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
  }

  .dark .form-input {
    background: #1f2937;
    border-color: #374151;
    color: #ffffff;
  }

  .dark .form-input:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }

  /* Grid Layout Fixes */
  .responsive-grid {
    display: grid;
    gap: 1.5rem;
    grid-template-columns: 1fr;
  }

  @media (min-width: 640px) {
    .responsive-grid {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  @media (min-width: 1024px) {
    .responsive-grid {
      grid-template-columns: repeat(3, 1fr);
    }
  }

  /* Text Direction for Arabic */
  .rtl-content {
    direction: rtl;
    text-align: right;
  }

  .ltr-content {
    direction: ltr;
    text-align: left;
  }

  /* Additional Layout Fixes */
  .hero-section {
    min-height: 60vh;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .card-grid {
    display: grid;
    gap: 2rem;
    grid-template-columns: 1fr;
  }

  @media (min-width: 768px) {
    .card-grid {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  @media (min-width: 1024px) {
    .card-grid {
      grid-template-columns: repeat(3, 1fr);
    }
  }

  /* Spacing Utilities */
  .section-spacing {
    margin-bottom: 3rem;
  }

  .content-spacing {
    margin-bottom: 1.5rem;
  }

  /* Typography Improvements */
  .heading-primary {
    font-size: 2.5rem;
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 1rem;
  }

  .heading-secondary {
    font-size: 2rem;
    font-weight: 600;
    line-height: 1.3;
    margin-bottom: 0.75rem;
  }

  .text-large {
    font-size: 1.125rem;
    line-height: 1.6;
  }

  @media (min-width: 768px) {
    .heading-primary {
      font-size: 3rem;
    }

    .heading-secondary {
      font-size: 2.25rem;
    }
  }

  @media (min-width: 1024px) {
    .heading-primary {
      font-size: 3.5rem;
    }

    .heading-secondary {
      font-size: 2.5rem;
    }
  }
}

@keyframes gradient-shift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes pulse-glow {
  0% {
    box-shadow: 0 0 20px rgba(37, 99, 235, 0.4);
  }
  100% {
    box-shadow: 0 0 40px rgba(37, 99, 235, 0.8);
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #2563eb, #f59e0b);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #1d4ed8, #d97706);
}
