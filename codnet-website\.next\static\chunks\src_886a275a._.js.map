{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/My%20Company%20Website/codnet-website/src/components/ui/Logo.tsx"], "sourcesContent": ["import Link from 'next/link';\n\ninterface LogoProps {\n  className?: string;\n  size?: 'sm' | 'md' | 'lg';\n}\n\nexport default function Logo({ className = '', size = 'md' }: LogoProps) {\n  const sizeClasses = {\n    sm: 'h-6 w-6 text-sm',\n    md: 'h-8 w-8 text-lg',\n    lg: 'h-12 w-12 text-2xl'\n  };\n\n  const textSizeClasses = {\n    sm: 'text-lg',\n    md: 'text-xl',\n    lg: 'text-3xl'\n  };\n\n  return (\n    <Link href=\"/\" className={`flex items-center space-x-2 rtl:space-x-reverse ${className}`}>\n      <div className={`${sizeClasses[size]} rounded-xl bg-gradient-to-br from-blue-600 via-blue-700 to-amber-500 flex items-center justify-center shadow-lg pulse-glow`}>\n        <span className={`text-white font-bold ${textSizeClasses[size]}`}>C</span>\n      </div>\n      <span className={`${textSizeClasses[size]} font-bold gradient-text`}>Codnet</span>\n    </Link>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAOe,SAAS,KAAK,EAAE,YAAY,EAAE,EAAE,OAAO,IAAI,EAAa;IACrE,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,kBAAkB;QACtB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC,+JAAA,CAAA,UAAI;QAAC,MAAK;QAAI,WAAW,CAAC,gDAAgD,EAAE,WAAW;;0BACtF,6LAAC;gBAAI,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,2HAA2H,CAAC;0BAC/J,cAAA,6LAAC;oBAAK,WAAW,CAAC,qBAAqB,EAAE,eAAe,CAAC,KAAK,EAAE;8BAAE;;;;;;;;;;;0BAEpE,6LAAC;gBAAK,WAAW,GAAG,eAAe,CAAC,KAAK,CAAC,wBAAwB,CAAC;0BAAE;;;;;;;;;;;;AAG3E;KArBwB", "debugId": null}}, {"offset": {"line": 71, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/My%20Company%20Website/codnet-website/src/components/layout/Header.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport { useTheme } from 'next-themes';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport {\n  Bars3Icon,\n  XMarkIcon,\n  SunIcon,\n  MoonIcon,\n  ChevronDownIcon\n} from '@heroicons/react/24/outline';\nimport Logo from '@/components/ui/Logo';\n\nconst navigation = [\n  { name: 'الرئيسية', href: '/' },\n  { \n    name: 'خدماتنا', \n    href: '/services',\n    submenu: [\n      { name: 'تطبيقات الهاتف', href: '/services/mobile' },\n      { name: 'تطبيقات الويب', href: '/services/web' },\n      { name: 'تطبيقات الحاسوب', href: '/services/desktop' },\n    ]\n  },\n  { name: 'معرض الأعمال', href: '/portfolio' },\n  { name: 'من نحن', href: '/about' },\n  { name: 'المدونة', href: '/blog' },\n  { name: 'الوظائف', href: '/careers' },\n  { name: 'تواصل معنا', href: '/contact' },\n];\n\nexport default function Header() {\n  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);\n  const [scrolled, setScrolled] = useState(false);\n  const { theme, setTheme } = useTheme();\n  const [mounted, setMounted] = useState(false);\n\n  useEffect(() => {\n    setMounted(true);\n    const handleScroll = () => {\n      setScrolled(window.scrollY > 20);\n    };\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  if (!mounted) return null;\n\n  return (\n    <header \n      className={`fixed top-0 w-full z-50 transition-all duration-300 ${\n        scrolled \n          ? 'bg-white/90 dark:bg-slate-900/90 backdrop-blur-md shadow-lg' \n          : 'bg-transparent'\n      }`}\n    >\n      <nav className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex h-16 items-center justify-between\">\n          {/* Logo */}\n          <div className=\"flex items-center\">\n            <Logo />\n          </div>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:block\">\n            <div className=\"flex items-center space-x-8 rtl:space-x-reverse\">\n              {navigation.map((item) => (\n                <div key={item.name} className=\"relative group\">\n                  <Link\n                    href={item.href}\n                    className=\"text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 px-3 py-2 text-sm font-medium transition-colors duration-200 flex items-center\"\n                  >\n                    {item.name}\n                    {item.submenu && (\n                      <ChevronDownIcon className=\"mr-1 h-4 w-4 transition-transform group-hover:rotate-180\" />\n                    )}\n                  </Link>\n                  \n                  {/* Submenu */}\n                  {item.submenu && (\n                    <div className=\"absolute right-0 mt-2 w-48 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 bg-white dark:bg-slate-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700\">\n                      <div className=\"py-2\">\n                        {item.submenu.map((subItem) => (\n                          <Link\n                            key={subItem.name}\n                            href={subItem.href}\n                            className=\"block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-slate-700 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200\"\n                          >\n                            {subItem.name}\n                          </Link>\n                        ))}\n                      </div>\n                    </div>\n                  )}\n                </div>\n              ))}\n            </div>\n          </div>\n\n          {/* Right side buttons */}\n          <div className=\"flex items-center space-x-4 rtl:space-x-reverse\">\n            {/* Theme toggle */}\n            <button\n              onClick={() => setTheme(theme === 'dark' ? 'light' : 'dark')}\n              className=\"p-2 rounded-lg bg-gray-100 dark:bg-slate-800 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-slate-700 transition-colors duration-200\"\n            >\n              {theme === 'dark' ? (\n                <SunIcon className=\"h-5 w-5\" />\n              ) : (\n                <MoonIcon className=\"h-5 w-5\" />\n              )}\n            </button>\n\n            {/* CTA Button */}\n            <Link\n              href=\"/contact\"\n              className=\"hidden md:inline-flex btn-primary items-center px-6 py-3 text-white font-semibold rounded-lg text-sm\"\n            >\n              ابدأ مشروعك\n            </Link>\n\n            {/* Mobile menu button */}\n            <button\n              type=\"button\"\n              className=\"md:hidden p-2 rounded-lg bg-gray-100 dark:bg-slate-800 text-gray-700 dark:text-gray-300\"\n              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}\n            >\n              {mobileMenuOpen ? (\n                <XMarkIcon className=\"h-6 w-6\" />\n              ) : (\n                <Bars3Icon className=\"h-6 w-6\" />\n              )}\n            </button>\n          </div>\n        </div>\n      </nav>\n\n      {/* Mobile menu */}\n      <AnimatePresence>\n        {mobileMenuOpen && (\n          <motion.div\n            initial={{ opacity: 0, height: 0 }}\n            animate={{ opacity: 1, height: 'auto' }}\n            exit={{ opacity: 0, height: 0 }}\n            className=\"md:hidden bg-white dark:bg-slate-900 border-t border-gray-200 dark:border-gray-700\"\n          >\n            <div className=\"px-4 py-6 space-y-4\">\n              {navigation.map((item) => (\n                <div key={item.name}>\n                  <Link\n                    href={item.href}\n                    className=\"block text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 py-2 text-base font-medium transition-colors duration-200\"\n                    onClick={() => setMobileMenuOpen(false)}\n                  >\n                    {item.name}\n                  </Link>\n                  {item.submenu && (\n                    <div className=\"mr-4 mt-2 space-y-2\">\n                      {item.submenu.map((subItem) => (\n                        <Link\n                          key={subItem.name}\n                          href={subItem.href}\n                          className=\"block text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 py-1 text-sm transition-colors duration-200\"\n                          onClick={() => setMobileMenuOpen(false)}\n                        >\n                          {subItem.name}\n                        </Link>\n                      ))}\n                    </div>\n                  )}\n                </div>\n              ))}\n              <Link\n                href=\"/contact\"\n                className=\"btn-primary block w-full text-center px-4 py-3 text-white font-semibold rounded-lg\"\n                onClick={() => setMobileMenuOpen(false)}\n              >\n                ابدأ مشروعك\n              </Link>\n            </div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAOA;;;AAbA;;;;;;;AAeA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAY,MAAM;IAAI;IAC9B;QACE,MAAM;QACN,MAAM;QACN,SAAS;YACP;gBAAE,MAAM;gBAAkB,MAAM;YAAmB;YACnD;gBAAE,MAAM;gBAAiB,MAAM;YAAgB;YAC/C;gBAAE,MAAM;gBAAmB,MAAM;YAAoB;SACtD;IACH;IACA;QAAE,MAAM;QAAgB,MAAM;IAAa;IAC3C;QAAE,MAAM;QAAU,MAAM;IAAS;IACjC;QAAE,MAAM;QAAW,MAAM;IAAQ;IACjC;QAAE,MAAM;QAAW,MAAM;IAAW;IACpC;QAAE,MAAM;QAAc,MAAM;IAAW;CACxC;AAEc,SAAS;;IACtB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,WAAW;YACX,MAAM;iDAAe;oBACnB,YAAY,OAAO,OAAO,GAAG;gBAC/B;;YACA,OAAO,gBAAgB,CAAC,UAAU;YAClC;oCAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;2BAAG,EAAE;IAEL,IAAI,CAAC,SAAS,OAAO;IAErB,qBACE,6LAAC;QACC,WAAW,CAAC,oDAAoD,EAC9D,WACI,gEACA,kBACJ;;0BAEF,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,mIAAA,CAAA,UAAI;;;;;;;;;;sCAIP,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC;wCAAoB,WAAU;;0DAC7B,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;;oDAET,KAAK,IAAI;oDACT,KAAK,OAAO,kBACX,6LAAC,gOAAA,CAAA,kBAAe;wDAAC,WAAU;;;;;;;;;;;;4CAK9B,KAAK,OAAO,kBACX,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;8DACZ,KAAK,OAAO,CAAC,GAAG,CAAC,CAAC,wBACjB,6LAAC,+JAAA,CAAA,UAAI;4DAEH,MAAM,QAAQ,IAAI;4DAClB,WAAU;sEAET,QAAQ,IAAI;2DAJR,QAAQ,IAAI;;;;;;;;;;;;;;;;uCAjBnB,KAAK,IAAI;;;;;;;;;;;;;;;sCAiCzB,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCACC,SAAS,IAAM,SAAS,UAAU,SAAS,UAAU;oCACrD,WAAU;8CAET,UAAU,uBACT,6LAAC,gNAAA,CAAA,UAAO;wCAAC,WAAU;;;;;6DAEnB,6LAAC,kNAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;8CAKxB,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAKD,6LAAC;oCACC,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,kBAAkB,CAAC;8CAEjC,+BACC,6LAAC,oNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;6DAErB,6LAAC,oNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ/B,6LAAC,4LAAA,CAAA,kBAAe;0BACb,gCACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBACjC,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAO;oBACtC,MAAM;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBAC9B,WAAU;8BAEV,cAAA,6LAAC;wBAAI,WAAU;;4BACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC;;sDACC,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAM,KAAK,IAAI;4CACf,WAAU;4CACV,SAAS,IAAM,kBAAkB;sDAEhC,KAAK,IAAI;;;;;;wCAEX,KAAK,OAAO,kBACX,6LAAC;4CAAI,WAAU;sDACZ,KAAK,OAAO,CAAC,GAAG,CAAC,CAAC,wBACjB,6LAAC,+JAAA,CAAA,UAAI;oDAEH,MAAM,QAAQ,IAAI;oDAClB,WAAU;oDACV,SAAS,IAAM,kBAAkB;8DAEhC,QAAQ,IAAI;mDALR,QAAQ,IAAI;;;;;;;;;;;mCAZjB,KAAK,IAAI;;;;;0CAwBrB,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,kBAAkB;0CAClC;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;GA1JwB;;QAGM,mJAAA,CAAA,WAAQ;;;KAHd", "debugId": null}}, {"offset": {"line": 419, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/My%20Company%20Website/codnet-website/src/components/layout/Footer.tsx"], "sourcesContent": ["import Link from 'next/link';\nimport {\n  MapPinIcon,\n  PhoneIcon,\n  EnvelopeIcon,\n  ArrowTopRightOnSquareIcon\n} from '@heroicons/react/24/outline';\nimport Logo from '@/components/ui/Logo';\n\nconst navigation = {\n  services: [\n    { name: 'تطبيقات الهاتف', href: '/services/mobile' },\n    { name: 'تطبيقات الويب', href: '/services/web' },\n    { name: 'تطبيقات الحاسوب', href: '/services/desktop' },\n    { name: 'استشارات تقنية', href: '/services/consulting' },\n  ],\n  company: [\n    { name: 'من نحن', href: '/about' },\n    { name: 'معرض الأعمال', href: '/portfolio' },\n    { name: 'المدونة', href: '/blog' },\n    { name: 'الوظائف', href: '/careers' },\n  ],\n  support: [\n    { name: 'تواصل معنا', href: '/contact' },\n    { name: 'الأسئلة الشائعة', href: '/faq' },\n    { name: 'الدعم التقني', href: '/support' },\n    { name: 'سياسة الخصوصية', href: '/privacy' },\n  ],\n  social: [\n    {\n      name: 'LinkedIn',\n      href: '#',\n      icon: (props: any) => (\n        <svg fill=\"currentColor\" viewBox=\"0 0 24 24\" {...props}>\n          <path d=\"M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z\"/>\n        </svg>\n      ),\n    },\n    {\n      name: 'Twitter',\n      href: '#',\n      icon: (props: any) => (\n        <svg fill=\"currentColor\" viewBox=\"0 0 24 24\" {...props}>\n          <path d=\"M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84\"/>\n        </svg>\n      ),\n    },\n    {\n      name: 'GitHub',\n      href: '#',\n      icon: (props: any) => (\n        <svg fill=\"currentColor\" viewBox=\"0 0 24 24\" {...props}>\n          <path d=\"M12 2C6.477 2 2 6.484 2 12.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0112 6.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.202 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.943.359.309.678.92.678 1.855 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0022 12.017C22 6.484 17.522 2 12 2z\"/>\n        </svg>\n      ),\n    },\n  ],\n};\n\nexport default function Footer() {\n  return (\n    <footer className=\"bg-gray-900 text-white\">\n      <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n        {/* Main footer content */}\n        <div className=\"py-16\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n            {/* Company info */}\n            <div className=\"lg:col-span-1\">\n              <div className=\"mb-4\">\n                <Logo className=\"text-white\" />\n              </div>\n              <p className=\"text-gray-300 mb-6 leading-relaxed\">\n                نصمم المستقبل الرقمي لعملك. شركة رائدة في تطوير البرمجيات والحلول التقنية المبتكرة.\n              </p>\n              \n              {/* Contact info */}\n              <div className=\"space-y-3\">\n                <div className=\"flex items-center space-x-3 rtl:space-x-reverse\">\n                  <PhoneIcon className=\"h-5 w-5 text-blue-400\" />\n                  <span className=\"text-gray-300\">+966 50 123 4567</span>\n                </div>\n                <div className=\"flex items-center space-x-3 rtl:space-x-reverse\">\n                  <EnvelopeIcon className=\"h-5 w-5 text-blue-400\" />\n                  <span className=\"text-gray-300\"><EMAIL></span>\n                </div>\n                <div className=\"flex items-center space-x-3 rtl:space-x-reverse\">\n                  <MapPinIcon className=\"h-5 w-5 text-blue-400\" />\n                  <span className=\"text-gray-300\">الرياض، المملكة العربية السعودية</span>\n                </div>\n              </div>\n            </div>\n\n            {/* Services */}\n            <div>\n              <h3 className=\"text-lg font-semibold mb-4\">خدماتنا</h3>\n              <ul className=\"space-y-3\">\n                {navigation.services.map((item) => (\n                  <li key={item.name}>\n                    <Link\n                      href={item.href}\n                      className=\"text-gray-300 hover:text-white transition-colors duration-200 flex items-center group\"\n                    >\n                      {item.name}\n                      <ArrowTopRightOnSquareIcon className=\"h-4 w-4 mr-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200\" />\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </div>\n\n            {/* Company */}\n            <div>\n              <h3 className=\"text-lg font-semibold mb-4\">الشركة</h3>\n              <ul className=\"space-y-3\">\n                {navigation.company.map((item) => (\n                  <li key={item.name}>\n                    <Link\n                      href={item.href}\n                      className=\"text-gray-300 hover:text-white transition-colors duration-200 flex items-center group\"\n                    >\n                      {item.name}\n                      <ArrowTopRightOnSquareIcon className=\"h-4 w-4 mr-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200\" />\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </div>\n\n            {/* Support */}\n            <div>\n              <h3 className=\"text-lg font-semibold mb-4\">الدعم</h3>\n              <ul className=\"space-y-3\">\n                {navigation.support.map((item) => (\n                  <li key={item.name}>\n                    <Link\n                      href={item.href}\n                      className=\"text-gray-300 hover:text-white transition-colors duration-200 flex items-center group\"\n                    >\n                      {item.name}\n                      <ArrowTopRightOnSquareIcon className=\"h-4 w-4 mr-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200\" />\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </div>\n          </div>\n        </div>\n\n        {/* Newsletter signup */}\n        <div className=\"border-t border-gray-800 py-8\">\n          <div className=\"flex flex-col md:flex-row items-center justify-between\">\n            <div className=\"mb-4 md:mb-0\">\n              <h3 className=\"text-lg font-semibold mb-2\">اشترك في نشرتنا الإخبارية</h3>\n              <p className=\"text-gray-300\">احصل على آخر الأخبار والتحديثات التقنية</p>\n            </div>\n            <div className=\"flex w-full md:w-auto\">\n              <input\n                type=\"email\"\n                placeholder=\"أدخل بريدك الإلكتروني\"\n                className=\"flex-1 md:w-64 px-4 py-2 bg-gray-800 border border-gray-700 rounded-r-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-white placeholder-gray-400\"\n              />\n              <button className=\"btn-primary px-6 py-2 text-white font-medium rounded-l-lg\">\n                اشتراك\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Bottom footer */}\n        <div className=\"border-t border-gray-800 py-6\">\n          <div className=\"flex flex-col md:flex-row items-center justify-between\">\n            <p className=\"text-gray-400 text-sm mb-4 md:mb-0\">\n              © 2024 Codnet. جميع الحقوق محفوظة.\n            </p>\n            \n            {/* Social links */}\n            <div className=\"flex space-x-6 rtl:space-x-reverse\">\n              {navigation.social.map((item) => (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className=\"text-gray-400 hover:text-white transition-colors duration-200\"\n                >\n                  <span className=\"sr-only\">{item.name}</span>\n                  <item.icon className=\"h-6 w-6\" />\n                </Link>\n              ))}\n            </div>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;AAAA;AAMA;;;;;AAEA,MAAM,aAAa;IACjB,UAAU;QACR;YAAE,MAAM;YAAkB,MAAM;QAAmB;QACnD;YAAE,MAAM;YAAiB,MAAM;QAAgB;QAC/C;YAAE,MAAM;YAAmB,MAAM;QAAoB;QACrD;YAAE,MAAM;YAAkB,MAAM;QAAuB;KACxD;IACD,SAAS;QACP;YAAE,MAAM;YAAU,MAAM;QAAS;QACjC;YAAE,MAAM;YAAgB,MAAM;QAAa;QAC3C;YAAE,MAAM;YAAW,MAAM;QAAQ;QACjC;YAAE,MAAM;YAAW,MAAM;QAAW;KACrC;IACD,SAAS;QACP;YAAE,MAAM;YAAc,MAAM;QAAW;QACvC;YAAE,MAAM;YAAmB,MAAM;QAAO;QACxC;YAAE,MAAM;YAAgB,MAAM;QAAW;QACzC;YAAE,MAAM;YAAkB,MAAM;QAAW;KAC5C;IACD,QAAQ;QACN;YACE,MAAM;YACN,MAAM;YACN,MAAM,CAAC,sBACL,6LAAC;oBAAI,MAAK;oBAAe,SAAQ;oBAAa,GAAG,KAAK;8BACpD,cAAA,6LAAC;wBAAK,GAAE;;;;;;;;;;;QAGd;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,CAAC,sBACL,6LAAC;oBAAI,MAAK;oBAAe,SAAQ;oBAAa,GAAG,KAAK;8BACpD,cAAA,6LAAC;wBAAK,GAAE;;;;;;;;;;;QAGd;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,CAAC,sBACL,6LAAC;oBAAI,MAAK;oBAAe,SAAQ;oBAAa,GAAG,KAAK;8BACpD,cAAA,6LAAC;wBAAK,GAAE;;;;;;;;;;;QAGd;KACD;AACH;AAEe,SAAS;IACtB,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,mIAAA,CAAA,UAAI;4CAAC,WAAU;;;;;;;;;;;kDAElB,6LAAC;wCAAE,WAAU;kDAAqC;;;;;;kDAKlD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,oNAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;kEACrB,6LAAC;wDAAK,WAAU;kEAAgB;;;;;;;;;;;;0DAElC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,0NAAA,CAAA,eAAY;wDAAC,WAAU;;;;;;kEACxB,6LAAC;wDAAK,WAAU;kEAAgB;;;;;;;;;;;;0DAElC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,sNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;kEACtB,6LAAC;wDAAK,WAAU;kEAAgB;;;;;;;;;;;;;;;;;;;;;;;;0CAMtC,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,6LAAC;wCAAG,WAAU;kDACX,WAAW,QAAQ,CAAC,GAAG,CAAC,CAAC,qBACxB,6LAAC;0DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAU;;wDAET,KAAK,IAAI;sEACV,6LAAC,oPAAA,CAAA,4BAAyB;4DAAC,WAAU;;;;;;;;;;;;+CANhC,KAAK,IAAI;;;;;;;;;;;;;;;;0CAcxB,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,6LAAC;wCAAG,WAAU;kDACX,WAAW,OAAO,CAAC,GAAG,CAAC,CAAC,qBACvB,6LAAC;0DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAU;;wDAET,KAAK,IAAI;sEACV,6LAAC,oPAAA,CAAA,4BAAyB;4DAAC,WAAU;;;;;;;;;;;;+CANhC,KAAK,IAAI;;;;;;;;;;;;;;;;0CAcxB,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,6LAAC;wCAAG,WAAU;kDACX,WAAW,OAAO,CAAC,GAAG,CAAC,CAAC,qBACvB,6LAAC;0DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAU;;wDAET,KAAK,IAAI;sEACV,6LAAC,oPAAA,CAAA,4BAAyB;4DAAC,WAAU;;;;;;;;;;;;+CANhC,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAgB5B,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,6LAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;0CAE/B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,WAAU;;;;;;kDAEZ,6LAAC;wCAAO,WAAU;kDAA4D;;;;;;;;;;;;;;;;;;;;;;;8BAQpF,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;0CAAqC;;;;;;0CAKlD,6LAAC;gCAAI,WAAU;0CACZ,WAAW,MAAM,CAAC,GAAG,CAAC,CAAC,qBACtB,6LAAC,+JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAU;;0DAEV,6LAAC;gDAAK,WAAU;0DAAW,KAAK,IAAI;;;;;;0DACpC,6LAAC,KAAK,IAAI;gDAAC,WAAU;;;;;;;uCALhB,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAchC;KAtIwB", "debugId": null}}, {"offset": {"line": 973, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/My%20Company%20Website/codnet-website/src/app/portfolio/page.tsx"], "sourcesContent": ["'use client';\n\nimport Header from '@/components/layout/Header';\nimport Footer from '@/components/layout/Footer';\nimport { motion } from 'framer-motion';\nimport { useState } from 'react';\nimport { ArrowTopRightOnSquareIcon, EyeIcon } from '@heroicons/react/24/outline';\n\nconst categories = ['الكل', 'تطبيقات الهاتف', 'مواقع الويب', 'تطبيقات الحاسوب'];\n\nconst projects = [\n  {\n    id: 1,\n    title: 'تطبيق التجارة الإلكترونية',\n    category: 'تطبيقات الهاتف',\n    description: 'تطبيق متكامل للتسوق الإلكتروني مع نظام دفع آمن وتتبع الطلبات',\n    image: '/api/placeholder/400/300',\n    technologies: ['React Native', 'Node.js', 'MongoDB'],\n    client: 'شركة التجارة الذكية',\n    year: '2024',\n    results: ['زيادة المبيعات 150%', 'تحسين تجربة المستخدم', '50K+ تحميل'],\n    href: '/portfolio/ecommerce-app'\n  },\n  {\n    id: 2,\n    title: 'منصة التعلم الإلكتروني',\n    category: 'مواقع الويب',\n    description: 'منصة تعليمية تفاعلية مع نظام إدارة الدورات والاختبارات',\n    image: '/api/placeholder/400/300',\n    technologies: ['Next.js', 'PostgreSQL', 'WebRTC'],\n    client: 'معهد التقنية المتقدمة',\n    year: '2024',\n    results: ['10K+ طالب مسجل', 'تقييم 4.8/5', 'نمو 200%'],\n    href: '/portfolio/learning-platform'\n  },\n  {\n    id: 3,\n    title: 'نظام إدارة المخزون',\n    category: 'تطبيقات الحاسوب',\n    description: 'برنامج شامل لإدارة المخزون والمبيعات مع تقارير تحليلية',\n    image: '/api/placeholder/400/300',\n    technologies: ['Electron', 'React', 'SQLite'],\n    client: 'مجموعة الأعمال المتحدة',\n    year: '2023',\n    results: ['توفير 40% من الوقت', 'دقة 99.5%', 'ROI 300%'],\n    href: '/portfolio/inventory-system'\n  },\n  {\n    id: 4,\n    title: 'تطبيق الصحة واللياقة',\n    category: 'تطبيقات الهاتف',\n    description: 'تطبيق لتتبع اللياقة البدنية والصحة مع مدرب شخصي ذكي',\n    image: '/api/placeholder/400/300',\n    technologies: ['Flutter', 'Firebase', 'AI/ML'],\n    client: 'مركز اللياقة الذكي',\n    year: '2024',\n    results: ['100K+ مستخدم نشط', 'تقييم 4.9/5', 'نمو 180%'],\n    href: '/portfolio/fitness-app'\n  },\n  {\n    id: 5,\n    title: 'موقع الشركة المؤسسية',\n    category: 'مواقع الويب',\n    description: 'موقع مؤسسي متطور مع نظام إدارة المحتوى ومتعدد اللغات',\n    image: '/api/placeholder/400/300',\n    technologies: ['Next.js', 'Strapi', 'Tailwind'],\n    client: 'الشركة العالمية للاستثمار',\n    year: '2023',\n    results: ['زيادة الزيارات 250%', 'تحسين SEO', 'تقليل معدل الارتداد 60%'],\n    href: '/portfolio/corporate-website'\n  },\n  {\n    id: 6,\n    title: 'برنامج المحاسبة المتقدم',\n    category: 'تطبيقات الحاسوب',\n    description: 'نظام محاسبي شامل مع تقارير مالية وضريبية متقدمة',\n    image: '/api/placeholder/400/300',\n    technologies: ['C#', '.NET', 'SQL Server'],\n    client: 'مكتب المحاسبة المتخصص',\n    year: '2023',\n    results: ['أتمتة 90% من العمليات', 'دقة 99.9%', 'توفير 50% من الوقت'],\n    href: '/portfolio/accounting-software'\n  }\n];\n\nexport default function PortfolioPage() {\n  const [activeCategory, setActiveCategory] = useState('الكل');\n  const [hoveredProject, setHoveredProject] = useState<number | null>(null);\n\n  const filteredProjects = activeCategory === 'الكل' \n    ? projects \n    : projects.filter(project => project.category === activeCategory);\n\n  return (\n    <div className=\"min-h-screen\">\n      <Header />\n      <main className=\"pt-20\">\n        {/* Hero Section */}\n        <section className=\"py-20 bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-slate-900 dark:via-blue-900 dark:to-indigo-900\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n            <motion.div\n              initial={{ opacity: 0, y: 30 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6 }}\n            >\n              <h1 className=\"text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-6\">\n                معرض أعمالنا\n              </h1>\n              <p className=\"text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto mb-8\">\n                اكتشف مجموعة من مشاريعنا الناجحة التي حققت نتائج استثنائية لعملائنا\n              </p>\n\n              {/* Category Filter */}\n              <div className=\"flex flex-wrap justify-center gap-4\">\n                {categories.map((category) => (\n                  <button\n                    key={category}\n                    onClick={() => setActiveCategory(category)}\n                    className={`px-6 py-3 rounded-full font-medium transition-all duration-300 ${\n                      activeCategory === category\n                        ? 'bg-blue-600 text-white shadow-lg'\n                        : 'bg-white dark:bg-slate-700 text-gray-700 dark:text-gray-300 hover:bg-blue-50 dark:hover:bg-slate-600'\n                    }`}\n                  >\n                    {category}\n                  </button>\n                ))}\n              </div>\n            </motion.div>\n          </div>\n        </section>\n\n        {/* Projects Grid */}\n        <section className=\"py-20 bg-white dark:bg-slate-900\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n            <motion.div\n              layout\n              className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-8\"\n            >\n              {filteredProjects.map((project, index) => (\n                <motion.div\n                  key={project.id}\n                  layout\n                  initial={{ opacity: 0, scale: 0.9 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  exit={{ opacity: 0, scale: 0.9 }}\n                  transition={{ duration: 0.5, delay: index * 0.1 }}\n                  className=\"group relative bg-white dark:bg-slate-800 rounded-2xl overflow-hidden shadow-lg card-hover\"\n                  onMouseEnter={() => setHoveredProject(project.id)}\n                  onMouseLeave={() => setHoveredProject(null)}\n                >\n                  {/* Project Image */}\n                  <div className=\"relative h-48 bg-gradient-to-br from-blue-500 to-purple-600 overflow-hidden\">\n                    {/* Placeholder for project image */}\n                    <div className=\"absolute inset-0 bg-gradient-to-br from-blue-500/20 to-purple-600/20\" />\n                    <div className=\"absolute inset-0 flex items-center justify-center\">\n                      <div className=\"text-white text-6xl opacity-20\">📱</div>\n                    </div>\n                    \n                    {/* Overlay */}\n                    <div className={`absolute inset-0 bg-black/50 transition-opacity duration-300 ${\n                      hoveredProject === project.id ? 'opacity-100' : 'opacity-0'\n                    }`}>\n                      <div className=\"absolute inset-0 flex items-center justify-center\">\n                        <a\n                          href={project.href}\n                          className=\"inline-flex items-center px-4 py-2 bg-white text-gray-900 rounded-lg font-medium hover:bg-gray-100 transition-colors duration-200\"\n                        >\n                          <EyeIcon className=\"h-5 w-5 ml-2\" />\n                          عرض المشروع\n                        </a>\n                      </div>\n                    </div>\n\n                    {/* Category Badge */}\n                    <div className=\"absolute top-4 right-4\">\n                      <span className=\"px-3 py-1 bg-white/90 text-gray-900 text-xs font-medium rounded-full\">\n                        {project.category}\n                      </span>\n                    </div>\n                  </div>\n\n                  {/* Project Content */}\n                  <div className=\"p-6\">\n                    <div className=\"flex items-center justify-between mb-2\">\n                      <h3 className=\"text-xl font-bold text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-300\">\n                        {project.title}\n                      </h3>\n                      <span className=\"text-sm text-gray-500 dark:text-gray-400\">\n                        {project.year}\n                      </span>\n                    </div>\n\n                    <p className=\"text-gray-600 dark:text-gray-300 mb-4 leading-relaxed\">\n                      {project.description}\n                    </p>\n\n                    {/* Client */}\n                    <p className=\"text-sm text-gray-500 dark:text-gray-400 mb-4\">\n                      العميل: {project.client}\n                    </p>\n\n                    {/* Technologies */}\n                    <div className=\"flex flex-wrap gap-2 mb-4\">\n                      {project.technologies.map((tech, techIndex) => (\n                        <span\n                          key={techIndex}\n                          className=\"px-2 py-1 text-xs font-medium bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200 rounded-md\"\n                        >\n                          {tech}\n                        </span>\n                      ))}\n                    </div>\n\n                    {/* Results */}\n                    <div className=\"space-y-1 mb-4\">\n                      {project.results.slice(0, 2).map((result, resultIndex) => (\n                        <div key={resultIndex} className=\"flex items-center text-sm text-gray-600 dark:text-gray-300\">\n                          <div className=\"w-1.5 h-1.5 rounded-full bg-green-500 ml-2 flex-shrink-0\" />\n                          {result}\n                        </div>\n                      ))}\n                    </div>\n\n                    {/* CTA */}\n                    <a\n                      href={project.href}\n                      className=\"inline-flex items-center text-sm font-semibold text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 transition-colors duration-300\"\n                    >\n                      اقرأ دراسة الحالة\n                      <ArrowTopRightOnSquareIcon className=\"mr-1 h-4 w-4 group-hover:-translate-y-0.5 group-hover:translate-x-0.5 transition-transform duration-300\" />\n                    </a>\n                  </div>\n                </motion.div>\n              ))}\n            </motion.div>\n          </div>\n        </section>\n      </main>\n      <Footer />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;;;AANA;;;;;;AAQA,MAAM,aAAa;IAAC;IAAQ;IAAkB;IAAe;CAAkB;AAE/E,MAAM,WAAW;IACf;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,aAAa;QACb,OAAO;QACP,cAAc;YAAC;YAAgB;YAAW;SAAU;QACpD,QAAQ;QACR,MAAM;QACN,SAAS;YAAC;YAAuB;YAAwB;SAAa;QACtE,MAAM;IACR;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,aAAa;QACb,OAAO;QACP,cAAc;YAAC;YAAW;YAAc;SAAS;QACjD,QAAQ;QACR,MAAM;QACN,SAAS;YAAC;YAAkB;YAAe;SAAW;QACtD,MAAM;IACR;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,aAAa;QACb,OAAO;QACP,cAAc;YAAC;YAAY;YAAS;SAAS;QAC7C,QAAQ;QACR,MAAM;QACN,SAAS;YAAC;YAAsB;YAAa;SAAW;QACxD,MAAM;IACR;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,aAAa;QACb,OAAO;QACP,cAAc;YAAC;YAAW;YAAY;SAAQ;QAC9C,QAAQ;QACR,MAAM;QACN,SAAS;YAAC;YAAoB;YAAe;SAAW;QACxD,MAAM;IACR;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,aAAa;QACb,OAAO;QACP,cAAc;YAAC;YAAW;YAAU;SAAW;QAC/C,QAAQ;QACR,MAAM;QACN,SAAS;YAAC;YAAuB;YAAa;SAA0B;QACxE,MAAM;IACR;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,aAAa;QACb,OAAO;QACP,cAAc;YAAC;YAAM;YAAQ;SAAa;QAC1C,QAAQ;QACR,MAAM;QACN,SAAS;YAAC;YAAyB;YAAa;SAAqB;QACrE,MAAM;IACR;CACD;AAEc,SAAS;;IACtB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAEpE,MAAM,mBAAmB,mBAAmB,SACxC,WACA,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,QAAQ,KAAK;IAEpD,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,yIAAA,CAAA,UAAM;;;;;0BACP,6LAAC;gBAAK,WAAU;;kCAEd,6LAAC;wBAAQ,WAAU;kCACjB,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;gCAAI;;kDAE5B,6LAAC;wCAAG,WAAU;kDAAoE;;;;;;kDAGlF,6LAAC;wCAAE,WAAU;kDAAkE;;;;;;kDAK/E,6LAAC;wCAAI,WAAU;kDACZ,WAAW,GAAG,CAAC,CAAC,yBACf,6LAAC;gDAEC,SAAS,IAAM,kBAAkB;gDACjC,WAAW,CAAC,+DAA+D,EACzE,mBAAmB,WACf,qCACA,wGACJ;0DAED;+CARI;;;;;;;;;;;;;;;;;;;;;;;;;;kCAiBjB,6LAAC;wBAAQ,WAAU;kCACjB,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,MAAM;gCACN,WAAU;0CAET,iBAAiB,GAAG,CAAC,CAAC,SAAS,sBAC9B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,MAAM;wCACN,SAAS;4CAAE,SAAS;4CAAG,OAAO;wCAAI;wCAClC,SAAS;4CAAE,SAAS;4CAAG,OAAO;wCAAE;wCAChC,MAAM;4CAAE,SAAS;4CAAG,OAAO;wCAAI;wCAC/B,YAAY;4CAAE,UAAU;4CAAK,OAAO,QAAQ;wCAAI;wCAChD,WAAU;wCACV,cAAc,IAAM,kBAAkB,QAAQ,EAAE;wCAChD,cAAc,IAAM,kBAAkB;;0DAGtC,6LAAC;gDAAI,WAAU;;kEAEb,6LAAC;wDAAI,WAAU;;;;;;kEACf,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;sEAAiC;;;;;;;;;;;kEAIlD,6LAAC;wDAAI,WAAW,CAAC,6DAA6D,EAC5E,mBAAmB,QAAQ,EAAE,GAAG,gBAAgB,aAChD;kEACA,cAAA,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEACC,MAAM,QAAQ,IAAI;gEAClB,WAAU;;kFAEV,6LAAC,gNAAA,CAAA,UAAO;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;;;;;;;;;;;kEAO1C,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAK,WAAU;sEACb,QAAQ,QAAQ;;;;;;;;;;;;;;;;;0DAMvB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAG,WAAU;0EACX,QAAQ,KAAK;;;;;;0EAEhB,6LAAC;gEAAK,WAAU;0EACb,QAAQ,IAAI;;;;;;;;;;;;kEAIjB,6LAAC;wDAAE,WAAU;kEACV,QAAQ,WAAW;;;;;;kEAItB,6LAAC;wDAAE,WAAU;;4DAAgD;4DAClD,QAAQ,MAAM;;;;;;;kEAIzB,6LAAC;wDAAI,WAAU;kEACZ,QAAQ,YAAY,CAAC,GAAG,CAAC,CAAC,MAAM,0BAC/B,6LAAC;gEAEC,WAAU;0EAET;+DAHI;;;;;;;;;;kEASX,6LAAC;wDAAI,WAAU;kEACZ,QAAQ,OAAO,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,QAAQ,4BACxC,6LAAC;gEAAsB,WAAU;;kFAC/B,6LAAC;wEAAI,WAAU;;;;;;oEACd;;+DAFO;;;;;;;;;;kEAQd,6LAAC;wDACC,MAAM,QAAQ,IAAI;wDAClB,WAAU;;4DACX;0EAEC,6LAAC,oPAAA,CAAA,4BAAyB;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;uCAzFpC,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;0BAkG3B,6LAAC,yIAAA,CAAA,UAAM;;;;;;;;;;;AAGb;GA7JwB;KAAA", "debugId": null}}]}