import Link from 'next/link';

interface LogoProps {
  className?: string;
  size?: 'sm' | 'md' | 'lg';
}

export default function Logo({ className = '', size = 'md' }: LogoProps) {
  const sizeClasses = {
    sm: 'h-6 w-6 text-sm',
    md: 'h-8 w-8 text-lg',
    lg: 'h-12 w-12 text-2xl'
  };

  const textSizeClasses = {
    sm: 'text-lg',
    md: 'text-xl',
    lg: 'text-3xl'
  };

  return (
    <Link href="/" className={`flex items-center space-x-2 rtl:space-x-reverse ${className}`}>
      <div className={`${sizeClasses[size]} rounded-xl bg-gradient-to-br from-blue-600 via-blue-700 to-amber-500 flex items-center justify-center shadow-lg pulse-glow`}>
        <span className={`text-white font-bold ${textSizeClasses[size]}`}>C</span>
      </div>
      <span className={`${textSizeClasses[size]} font-bold gradient-text`}>Codnet</span>
    </Link>
  );
}
