'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import {
  ArrowLeftIcon,
  PhoneIcon,
  EnvelopeIcon,
  ChatBubbleLeftRightIcon,
  RocketLaunchIcon
} from '@heroicons/react/24/outline';

const contactMethods = [
  {
    icon: PhoneIcon,
    title: 'اتصل بنا',
    description: 'تحدث مع خبرائنا مباشرة',
    action: '+966 50 123 4567',
    href: 'tel:+966501234567',
    color: 'from-green-500 to-emerald-500'
  },
  {
    icon: EnvelopeIcon,
    title: 'راسلنا',
    description: 'احصل على رد خلال 24 ساعة',
    action: '<EMAIL>',
    href: 'mailto:<EMAIL>',
    color: 'from-blue-500 to-cyan-500'
  },
  {
    icon: ChatBubbleLeftRightIcon,
    title: 'دردشة مباشرة',
    description: 'تواصل فوري مع فريقنا',
    action: 'ابدأ المحادثة',
    href: '#chat',
    color: 'from-purple-500 to-pink-500'
  }
];

const benefits = [
  'استشارة مجانية لمدة 30 دقيقة',
  'تقدير مبدئي للتكلفة والوقت',
  'خطة مشروع مفصلة',
  'ضمان الجودة والتسليم في الوقت المحدد'
];

export default function CTASection() {
  return (
    <section className="py-20 bg-gradient-to-br from-blue-900 via-indigo-900 to-purple-900 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0">
        {/* Animated background shapes */}
        <motion.div
          className="absolute top-20 left-10 w-72 h-72 bg-blue-400/20 rounded-full blur-3xl"
          animate={{
            x: [0, 100, 0],
            y: [0, -50, 0],
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: "linear"
          }}
        />
        <motion.div
          className="absolute bottom-20 right-10 w-96 h-96 bg-purple-400/20 rounded-full blur-3xl"
          animate={{
            x: [0, -80, 0],
            y: [0, 60, 0],
          }}
          transition={{
            duration: 25,
            repeat: Infinity,
            ease: "linear"
          }}
        />
        
        {/* Grid pattern */}
        <div className="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%239C92AC" fill-opacity="0.1"%3E%3Ccircle cx="30" cy="30" r="1"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-40" />
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Left Content */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
            className="text-center lg:text-right"
          >
            {/* Badge */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.2 }}
              className="inline-flex items-center px-4 py-2 rounded-full bg-white/10 backdrop-blur-sm text-white text-sm font-medium mb-6"
            >
              <RocketLaunchIcon className="h-4 w-4 ml-2" />
              ابدأ رحلتك الرقمية اليوم
            </motion.div>

            {/* Main Heading */}
            <motion.h2
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.3 }}
              className="text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-6 leading-tight"
            >
              هل أنت مستعد لتحويل{' '}
              <span className="bg-gradient-to-r from-yellow-400 to-orange-400 bg-clip-text text-transparent">
                فكرتك
              </span>
              {' '}إلى واقع؟
            </motion.h2>

            {/* Subtitle */}
            <motion.p
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.4 }}
              className="text-lg text-blue-100 mb-8 leading-relaxed max-w-2xl mx-auto lg:mx-0"
            >
              انضم إلى أكثر من 50 عميل راضي واكتشف كيف يمكن لحلولنا التقنية المبتكرة أن تدفع نمو عملك إلى آفاق جديدة.
            </motion.p>

            {/* Benefits */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.5 }}
              className="space-y-3 mb-8"
            >
              {benefits.map((benefit, index) => (
                <motion.div
                  key={benefit}
                  initial={{ opacity: 0, x: -20 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  viewport={{ once: true }}
                  transition={{ delay: 0.6 + index * 0.1 }}
                  className="flex items-center text-blue-100"
                >
                  <div className="w-2 h-2 rounded-full bg-yellow-400 ml-3 flex-shrink-0" />
                  {benefit}
                </motion.div>
              ))}
            </motion.div>

            {/* Main CTA Button */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.7 }}
              className="mb-8"
            >
              <Link
                href="/contact"
                className="group inline-flex items-center px-8 py-4 bg-gradient-to-r from-yellow-400 to-orange-500 hover:from-yellow-500 hover:to-orange-600 text-gray-900 font-bold rounded-xl shadow-2xl hover:shadow-3xl transition-all duration-300 transform hover:-translate-y-1 hover:scale-105"
              >
                احصل على استشارة مجانية
                <ArrowLeftIcon className="mr-2 h-5 w-5 group-hover:-translate-x-1 transition-transform duration-300" />
              </Link>
            </motion.div>

            {/* Trust indicators */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.8 }}
              className="flex flex-wrap justify-center lg:justify-start items-center gap-6 text-blue-200 text-sm"
            >
              <div className="flex items-center">
                <span className="text-yellow-400 ml-1">⭐</span>
                تقييم 5/5 نجوم
              </div>
              <div className="flex items-center">
                <span className="text-green-400 ml-1">✓</span>
                ضمان الجودة
              </div>
              <div className="flex items-center">
                <span className="text-blue-400 ml-1">🚀</span>
                تسليم سريع
              </div>
            </motion.div>
          </motion.div>

          {/* Right Content - Contact Methods */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="space-y-6"
          >
            <h3 className="text-2xl font-bold text-white mb-8 text-center">
              طرق التواصل المتاحة
            </h3>

            {contactMethods.map((method, index) => (
              <motion.div
                key={method.title}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: 0.9 + index * 0.1 }}
              >
                <Link
                  href={method.href}
                  className="group block p-6 bg-white/10 backdrop-blur-sm rounded-2xl border border-white/20 hover:bg-white/20 transition-all duration-300 hover:-translate-y-1 hover:shadow-2xl"
                >
                  <div className="flex items-center">
                    <div className={`p-3 rounded-xl bg-gradient-to-br ${method.color} text-white group-hover:scale-110 transition-transform duration-300`}>
                      <method.icon className="h-6 w-6" />
                    </div>
                    <div className="mr-4 flex-1">
                      <h4 className="text-lg font-semibold text-white mb-1">
                        {method.title}
                      </h4>
                      <p className="text-blue-200 text-sm mb-2">
                        {method.description}
                      </p>
                      <p className="text-yellow-400 font-medium">
                        {method.action}
                      </p>
                    </div>
                    <ArrowLeftIcon className="h-5 w-5 text-white/60 group-hover:text-white group-hover:-translate-x-1 transition-all duration-300" />
                  </div>
                </Link>
              </motion.div>
            ))}

            {/* Emergency contact */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 1.2 }}
              className="p-4 bg-red-500/20 backdrop-blur-sm rounded-xl border border-red-400/30 text-center"
            >
              <p className="text-red-200 text-sm mb-2">
                هل لديك مشروع عاجل؟
              </p>
              <Link
                href="tel:+966501234567"
                className="text-red-300 font-semibold hover:text-red-100 transition-colors duration-200"
              >
                اتصل الآن: +966 50 123 4567
              </Link>
            </motion.div>
          </motion.div>
        </div>

        {/* Bottom section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ delay: 1.3 }}
          className="mt-16 text-center"
        >
          <p className="text-blue-200 mb-4">
            أو تفضل زيارتنا شخصياً؟
          </p>
          <p className="text-white font-medium">
            📍 الرياض، المملكة العربية السعودية
          </p>
        </motion.div>
      </div>
    </section>
  );
}
