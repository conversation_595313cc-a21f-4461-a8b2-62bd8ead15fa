import Link from 'next/link';
import { motion } from 'framer-motion';
import { ArrowLeftIcon } from '@heroicons/react/24/outline';

export default function CTASection() {
  return (
    <section className="py-20 bg-gradient-to-br from-blue-900 via-indigo-900 to-purple-900 text-white relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0">
        <motion.div
          className="absolute top-20 left-10 w-72 h-72 bg-blue-400/20 rounded-full blur-3xl"
          animate={{
            x: [0, 100, 0],
            y: [0, -50, 0],
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: "linear"
          }}
        />
        <motion.div
          className="absolute bottom-20 right-10 w-96 h-96 bg-purple-400/20 rounded-full blur-3xl"
          animate={{
            x: [0, -80, 0],
            y: [0, 60, 0],
          }}
          transition={{
            duration: 25,
            repeat: Infinity,
            ease: "linear"
          }}
        />
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
        >
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6 leading-tight">
            هل أنت مستعد لتحويل{' '}
            <span className="bg-gradient-to-r from-yellow-400 to-orange-400 bg-clip-text text-transparent">
              فكرتك
            </span>
            {' '}إلى واقع؟
          </h2>
          <p className="text-xl mb-8 text-blue-100 max-w-3xl mx-auto">
            انضم إلى أكثر من 50 عميل راضي واكتشف كيف يمكن لحلولنا التقنية المبتكرة أن تدفع نمو عملك إلى آفاق جديدة
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-8">
            <Link
              href="/contact"
              className="btn-secondary inline-flex items-center px-10 py-5 text-white font-bold rounded-xl text-xl shadow-2xl"
            >
              احصل على استشارة مجانية
              <ArrowLeftIcon className="mr-2 h-6 w-6" />
            </Link>

            <div className="flex items-center space-x-6 rtl:space-x-reverse text-blue-200 text-sm">
              <div className="flex items-center">
                <span className="text-yellow-400 ml-1">⭐</span>
                تقييم 5/5 نجوم
              </div>
              <div className="flex items-center">
                <span className="text-green-400 ml-1">✓</span>
                ضمان الجودة
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
            {[
              { icon: '📞', title: 'اتصل بنا', desc: '+966 50 123 4567' },
              { icon: '✉️', title: 'راسلنا', desc: '<EMAIL>' },
              { icon: '📍', title: 'زورنا', desc: 'الرياض، السعودية' }
            ].map((contact, index) => (
              <motion.div
                key={contact.title}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: 0.2 + index * 0.1 }}
                className="p-4 bg-white/10 backdrop-blur-sm rounded-xl border border-white/20"
              >
                <div className="text-2xl mb-2">{contact.icon}</div>
                <h3 className="font-semibold mb-1">{contact.title}</h3>
                <p className="text-blue-200 text-sm">{contact.desc}</p>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  );
}
