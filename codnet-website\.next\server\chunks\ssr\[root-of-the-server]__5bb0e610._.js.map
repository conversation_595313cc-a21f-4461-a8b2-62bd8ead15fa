{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/My%20Company%20Website/codnet-website/src/components/ui/Logo.tsx"], "sourcesContent": ["import Link from 'next/link';\n\ninterface LogoProps {\n  className?: string;\n  size?: 'sm' | 'md' | 'lg';\n}\n\nexport default function Logo({ className = '', size = 'md' }: LogoProps) {\n  const sizeClasses = {\n    sm: 'h-6 w-6 text-sm',\n    md: 'h-8 w-8 text-lg',\n    lg: 'h-12 w-12 text-2xl'\n  };\n\n  const textSizeClasses = {\n    sm: 'text-lg',\n    md: 'text-xl',\n    lg: 'text-3xl'\n  };\n\n  return (\n    <Link href=\"/\" className={`flex items-center space-x-2 rtl:space-x-reverse ${className}`}>\n      <div className={`${sizeClasses[size]} rounded-xl bg-gradient-to-br from-blue-600 via-blue-700 to-amber-500 flex items-center justify-center shadow-lg pulse-glow`}>\n        <span className={`text-white font-bold ${textSizeClasses[size]}`}>C</span>\n      </div>\n      <span className={`${textSizeClasses[size]} font-bold gradient-text`}>Codnet</span>\n    </Link>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAOe,SAAS,KAAK,EAAE,YAAY,EAAE,EAAE,OAAO,IAAI,EAAa;IACrE,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,kBAAkB;QACtB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC,4JAAA,CAAA,UAAI;QAAC,MAAK;QAAI,WAAW,CAAC,gDAAgD,EAAE,WAAW;;0BACtF,8OAAC;gBAAI,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,2HAA2H,CAAC;0BAC/J,cAAA,8OAAC;oBAAK,WAAW,CAAC,qBAAqB,EAAE,eAAe,CAAC,KAAK,EAAE;8BAAE;;;;;;;;;;;0BAEpE,8OAAC;gBAAK,WAAW,GAAG,eAAe,CAAC,KAAK,CAAC,wBAAwB,CAAC;0BAAE;;;;;;;;;;;;AAG3E", "debugId": null}}, {"offset": {"line": 89, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/My%20Company%20Website/codnet-website/src/components/layout/Header.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport { useTheme } from 'next-themes';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport {\n  Bars3Icon,\n  XMarkIcon,\n  SunIcon,\n  MoonIcon,\n  ChevronDownIcon\n} from '@heroicons/react/24/outline';\nimport Logo from '@/components/ui/Logo';\n\nconst navigation = [\n  { name: 'الرئيسية', href: '/' },\n  { \n    name: 'خدماتنا', \n    href: '/services',\n    submenu: [\n      { name: 'تطبيقات الهاتف', href: '/services/mobile' },\n      { name: 'تطبيقات الويب', href: '/services/web' },\n      { name: 'تطبيقات الحاسوب', href: '/services/desktop' },\n    ]\n  },\n  { name: 'معرض الأعمال', href: '/portfolio' },\n  { name: 'من نحن', href: '/about' },\n  { name: 'المدونة', href: '/blog' },\n  { name: 'الوظائف', href: '/careers' },\n  { name: 'تواصل معنا', href: '/contact' },\n];\n\nexport default function Header() {\n  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);\n  const [scrolled, setScrolled] = useState(false);\n  const { theme, setTheme } = useTheme();\n  const [mounted, setMounted] = useState(false);\n\n  useEffect(() => {\n    setMounted(true);\n    const handleScroll = () => {\n      setScrolled(window.scrollY > 20);\n    };\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  if (!mounted) return null;\n\n  return (\n    <header \n      className={`fixed top-0 w-full z-50 transition-all duration-300 ${\n        scrolled \n          ? 'bg-white/90 dark:bg-slate-900/90 backdrop-blur-md shadow-lg' \n          : 'bg-transparent'\n      }`}\n    >\n      <nav className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex h-16 items-center justify-between\">\n          {/* Logo */}\n          <div className=\"flex items-center\">\n            <Logo />\n          </div>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:block\">\n            <div className=\"flex items-center space-x-8 rtl:space-x-reverse\">\n              {navigation.map((item) => (\n                <div key={item.name} className=\"relative group\">\n                  <Link\n                    href={item.href}\n                    className=\"text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 px-3 py-2 text-sm font-medium transition-colors duration-200 flex items-center\"\n                  >\n                    {item.name}\n                    {item.submenu && (\n                      <ChevronDownIcon className=\"mr-1 h-4 w-4 transition-transform group-hover:rotate-180\" />\n                    )}\n                  </Link>\n                  \n                  {/* Submenu */}\n                  {item.submenu && (\n                    <div className=\"absolute right-0 mt-2 w-48 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 bg-white dark:bg-slate-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700\">\n                      <div className=\"py-2\">\n                        {item.submenu.map((subItem) => (\n                          <Link\n                            key={subItem.name}\n                            href={subItem.href}\n                            className=\"block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-slate-700 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200\"\n                          >\n                            {subItem.name}\n                          </Link>\n                        ))}\n                      </div>\n                    </div>\n                  )}\n                </div>\n              ))}\n            </div>\n          </div>\n\n          {/* Right side buttons */}\n          <div className=\"flex items-center space-x-4 rtl:space-x-reverse\">\n            {/* Theme toggle */}\n            <button\n              onClick={() => setTheme(theme === 'dark' ? 'light' : 'dark')}\n              className=\"p-2 rounded-lg bg-gray-100 dark:bg-slate-800 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-slate-700 transition-colors duration-200\"\n            >\n              {theme === 'dark' ? (\n                <SunIcon className=\"h-5 w-5\" />\n              ) : (\n                <MoonIcon className=\"h-5 w-5\" />\n              )}\n            </button>\n\n            {/* CTA Button */}\n            <Link\n              href=\"/contact\"\n              className=\"hidden md:inline-flex btn-primary items-center px-6 py-3 text-white font-semibold rounded-lg text-sm\"\n            >\n              ابدأ مشروعك\n            </Link>\n\n            {/* Mobile menu button */}\n            <button\n              type=\"button\"\n              className=\"md:hidden p-2 rounded-lg bg-gray-100 dark:bg-slate-800 text-gray-700 dark:text-gray-300\"\n              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}\n            >\n              {mobileMenuOpen ? (\n                <XMarkIcon className=\"h-6 w-6\" />\n              ) : (\n                <Bars3Icon className=\"h-6 w-6\" />\n              )}\n            </button>\n          </div>\n        </div>\n      </nav>\n\n      {/* Mobile menu */}\n      <AnimatePresence>\n        {mobileMenuOpen && (\n          <motion.div\n            initial={{ opacity: 0, height: 0 }}\n            animate={{ opacity: 1, height: 'auto' }}\n            exit={{ opacity: 0, height: 0 }}\n            className=\"md:hidden bg-white dark:bg-slate-900 border-t border-gray-200 dark:border-gray-700\"\n          >\n            <div className=\"px-4 py-6 space-y-4\">\n              {navigation.map((item) => (\n                <div key={item.name}>\n                  <Link\n                    href={item.href}\n                    className=\"block text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 py-2 text-base font-medium transition-colors duration-200\"\n                    onClick={() => setMobileMenuOpen(false)}\n                  >\n                    {item.name}\n                  </Link>\n                  {item.submenu && (\n                    <div className=\"mr-4 mt-2 space-y-2\">\n                      {item.submenu.map((subItem) => (\n                        <Link\n                          key={subItem.name}\n                          href={subItem.href}\n                          className=\"block text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 py-1 text-sm transition-colors duration-200\"\n                          onClick={() => setMobileMenuOpen(false)}\n                        >\n                          {subItem.name}\n                        </Link>\n                      ))}\n                    </div>\n                  )}\n                </div>\n              ))}\n              <Link\n                href=\"/contact\"\n                className=\"btn-primary block w-full text-center px-4 py-3 text-white font-semibold rounded-lg\"\n                onClick={() => setMobileMenuOpen(false)}\n              >\n                ابدأ مشروعك\n              </Link>\n            </div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAOA;AAbA;;;;;;;;AAeA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAY,MAAM;IAAI;IAC9B;QACE,MAAM;QACN,MAAM;QACN,SAAS;YACP;gBAAE,MAAM;gBAAkB,MAAM;YAAmB;YACnD;gBAAE,MAAM;gBAAiB,MAAM;YAAgB;YAC/C;gBAAE,MAAM;gBAAmB,MAAM;YAAoB;SACtD;IACH;IACA;QAAE,MAAM;QAAgB,MAAM;IAAa;IAC3C;QAAE,MAAM;QAAU,MAAM;IAAS;IACjC;QAAE,MAAM;QAAW,MAAM;IAAQ;IACjC;QAAE,MAAM;QAAW,MAAM;IAAW;IACpC;QAAE,MAAM;QAAc,MAAM;IAAW;CACxC;AAEc,SAAS;IACtB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;QACX,MAAM,eAAe;YACnB,YAAY,OAAO,OAAO,GAAG;QAC/B;QACA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,IAAI,CAAC,SAAS,OAAO;IAErB,qBACE,8OAAC;QACC,WAAW,CAAC,oDAAoD,EAC9D,WACI,gEACA,kBACJ;;0BAEF,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,gIAAA,CAAA,UAAI;;;;;;;;;;sCAIP,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC;wCAAoB,WAAU;;0DAC7B,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;;oDAET,KAAK,IAAI;oDACT,KAAK,OAAO,kBACX,8OAAC,6NAAA,CAAA,kBAAe;wDAAC,WAAU;;;;;;;;;;;;4CAK9B,KAAK,OAAO,kBACX,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;8DACZ,KAAK,OAAO,CAAC,GAAG,CAAC,CAAC,wBACjB,8OAAC,4JAAA,CAAA,UAAI;4DAEH,MAAM,QAAQ,IAAI;4DAClB,WAAU;sEAET,QAAQ,IAAI;2DAJR,QAAQ,IAAI;;;;;;;;;;;;;;;;uCAjBnB,KAAK,IAAI;;;;;;;;;;;;;;;sCAiCzB,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCACC,SAAS,IAAM,SAAS,UAAU,SAAS,UAAU;oCACrD,WAAU;8CAET,UAAU,uBACT,8OAAC,6MAAA,CAAA,UAAO;wCAAC,WAAU;;;;;6DAEnB,8OAAC,+MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;8CAKxB,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAKD,8OAAC;oCACC,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,kBAAkB,CAAC;8CAEjC,+BACC,8OAAC,iNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;6DAErB,8OAAC,iNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ/B,8OAAC,yLAAA,CAAA,kBAAe;0BACb,gCACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBACjC,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAO;oBACtC,MAAM;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBAC9B,WAAU;8BAEV,cAAA,8OAAC;wBAAI,WAAU;;4BACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC;;sDACC,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAM,KAAK,IAAI;4CACf,WAAU;4CACV,SAAS,IAAM,kBAAkB;sDAEhC,KAAK,IAAI;;;;;;wCAEX,KAAK,OAAO,kBACX,8OAAC;4CAAI,WAAU;sDACZ,KAAK,OAAO,CAAC,GAAG,CAAC,CAAC,wBACjB,8OAAC,4JAAA,CAAA,UAAI;oDAEH,MAAM,QAAQ,IAAI;oDAClB,WAAU;oDACV,SAAS,IAAM,kBAAkB;8DAEhC,QAAQ,IAAI;mDALR,QAAQ,IAAI;;;;;;;;;;;mCAZjB,KAAK,IAAI;;;;;0CAwBrB,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,kBAAkB;0CAClC;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf", "debugId": null}}, {"offset": {"line": 418, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/My%20Company%20Website/codnet-website/src/components/layout/Footer.tsx"], "sourcesContent": ["import Link from 'next/link';\nimport {\n  MapPinIcon,\n  PhoneIcon,\n  EnvelopeIcon,\n  ArrowTopRightOnSquareIcon\n} from '@heroicons/react/24/outline';\nimport Logo from '@/components/ui/Logo';\n\nconst navigation = {\n  services: [\n    { name: 'تطبيقات الهاتف', href: '/services/mobile' },\n    { name: 'تطبيقات الويب', href: '/services/web' },\n    { name: 'تطبيقات الحاسوب', href: '/services/desktop' },\n    { name: 'استشارات تقنية', href: '/services/consulting' },\n  ],\n  company: [\n    { name: 'من نحن', href: '/about' },\n    { name: 'معرض الأعمال', href: '/portfolio' },\n    { name: 'المدونة', href: '/blog' },\n    { name: 'الوظائف', href: '/careers' },\n  ],\n  support: [\n    { name: 'تواصل معنا', href: '/contact' },\n    { name: 'الأسئلة الشائعة', href: '/faq' },\n    { name: 'الدعم التقني', href: '/support' },\n    { name: 'سياسة الخصوصية', href: '/privacy' },\n  ],\n  social: [\n    {\n      name: 'LinkedIn',\n      href: '#',\n      icon: (props: any) => (\n        <svg fill=\"currentColor\" viewBox=\"0 0 24 24\" {...props}>\n          <path d=\"M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z\"/>\n        </svg>\n      ),\n    },\n    {\n      name: 'Twitter',\n      href: '#',\n      icon: (props: any) => (\n        <svg fill=\"currentColor\" viewBox=\"0 0 24 24\" {...props}>\n          <path d=\"M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84\"/>\n        </svg>\n      ),\n    },\n    {\n      name: 'GitHub',\n      href: '#',\n      icon: (props: any) => (\n        <svg fill=\"currentColor\" viewBox=\"0 0 24 24\" {...props}>\n          <path d=\"M12 2C6.477 2 2 6.484 2 12.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0112 6.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.202 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.943.359.309.678.92.678 1.855 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0022 12.017C22 6.484 17.522 2 12 2z\"/>\n        </svg>\n      ),\n    },\n  ],\n};\n\nexport default function Footer() {\n  return (\n    <footer className=\"bg-gray-900 text-white\">\n      <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n        {/* Main footer content */}\n        <div className=\"py-16\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n            {/* Company info */}\n            <div className=\"lg:col-span-1\">\n              <div className=\"mb-4\">\n                <Logo className=\"text-white\" />\n              </div>\n              <p className=\"text-gray-300 mb-6 leading-relaxed\">\n                نصمم المستقبل الرقمي لعملك. شركة رائدة في تطوير البرمجيات والحلول التقنية المبتكرة.\n              </p>\n              \n              {/* Contact info */}\n              <div className=\"space-y-3\">\n                <div className=\"flex items-center space-x-3 rtl:space-x-reverse\">\n                  <PhoneIcon className=\"h-5 w-5 text-blue-400\" />\n                  <span className=\"text-gray-300\">+966 50 123 4567</span>\n                </div>\n                <div className=\"flex items-center space-x-3 rtl:space-x-reverse\">\n                  <EnvelopeIcon className=\"h-5 w-5 text-blue-400\" />\n                  <span className=\"text-gray-300\"><EMAIL></span>\n                </div>\n                <div className=\"flex items-center space-x-3 rtl:space-x-reverse\">\n                  <MapPinIcon className=\"h-5 w-5 text-blue-400\" />\n                  <span className=\"text-gray-300\">الرياض، المملكة العربية السعودية</span>\n                </div>\n              </div>\n            </div>\n\n            {/* Services */}\n            <div>\n              <h3 className=\"text-lg font-semibold mb-4\">خدماتنا</h3>\n              <ul className=\"space-y-3\">\n                {navigation.services.map((item) => (\n                  <li key={item.name}>\n                    <Link\n                      href={item.href}\n                      className=\"text-gray-300 hover:text-white transition-colors duration-200 flex items-center group\"\n                    >\n                      {item.name}\n                      <ArrowTopRightOnSquareIcon className=\"h-4 w-4 mr-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200\" />\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </div>\n\n            {/* Company */}\n            <div>\n              <h3 className=\"text-lg font-semibold mb-4\">الشركة</h3>\n              <ul className=\"space-y-3\">\n                {navigation.company.map((item) => (\n                  <li key={item.name}>\n                    <Link\n                      href={item.href}\n                      className=\"text-gray-300 hover:text-white transition-colors duration-200 flex items-center group\"\n                    >\n                      {item.name}\n                      <ArrowTopRightOnSquareIcon className=\"h-4 w-4 mr-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200\" />\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </div>\n\n            {/* Support */}\n            <div>\n              <h3 className=\"text-lg font-semibold mb-4\">الدعم</h3>\n              <ul className=\"space-y-3\">\n                {navigation.support.map((item) => (\n                  <li key={item.name}>\n                    <Link\n                      href={item.href}\n                      className=\"text-gray-300 hover:text-white transition-colors duration-200 flex items-center group\"\n                    >\n                      {item.name}\n                      <ArrowTopRightOnSquareIcon className=\"h-4 w-4 mr-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200\" />\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </div>\n          </div>\n        </div>\n\n        {/* Newsletter signup */}\n        <div className=\"border-t border-gray-800 py-8\">\n          <div className=\"flex flex-col md:flex-row items-center justify-between\">\n            <div className=\"mb-4 md:mb-0\">\n              <h3 className=\"text-lg font-semibold mb-2\">اشترك في نشرتنا الإخبارية</h3>\n              <p className=\"text-gray-300\">احصل على آخر الأخبار والتحديثات التقنية</p>\n            </div>\n            <div className=\"flex w-full md:w-auto\">\n              <input\n                type=\"email\"\n                placeholder=\"أدخل بريدك الإلكتروني\"\n                className=\"flex-1 md:w-64 px-4 py-2 bg-gray-800 border border-gray-700 rounded-r-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-white placeholder-gray-400\"\n              />\n              <button className=\"btn-primary px-6 py-2 text-white font-medium rounded-l-lg\">\n                اشتراك\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Bottom footer */}\n        <div className=\"border-t border-gray-800 py-6\">\n          <div className=\"flex flex-col md:flex-row items-center justify-between\">\n            <p className=\"text-gray-400 text-sm mb-4 md:mb-0\">\n              © 2024 Codnet. جميع الحقوق محفوظة.\n            </p>\n            \n            {/* Social links */}\n            <div className=\"flex space-x-6 rtl:space-x-reverse\">\n              {navigation.social.map((item) => (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className=\"text-gray-400 hover:text-white transition-colors duration-200\"\n                >\n                  <span className=\"sr-only\">{item.name}</span>\n                  <item.icon className=\"h-6 w-6\" />\n                </Link>\n              ))}\n            </div>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;AAAA;AAMA;;;;;AAEA,MAAM,aAAa;IACjB,UAAU;QACR;YAAE,MAAM;YAAkB,MAAM;QAAmB;QACnD;YAAE,MAAM;YAAiB,MAAM;QAAgB;QAC/C;YAAE,MAAM;YAAmB,MAAM;QAAoB;QACrD;YAAE,MAAM;YAAkB,MAAM;QAAuB;KACxD;IACD,SAAS;QACP;YAAE,MAAM;YAAU,MAAM;QAAS;QACjC;YAAE,MAAM;YAAgB,MAAM;QAAa;QAC3C;YAAE,MAAM;YAAW,MAAM;QAAQ;QACjC;YAAE,MAAM;YAAW,MAAM;QAAW;KACrC;IACD,SAAS;QACP;YAAE,MAAM;YAAc,MAAM;QAAW;QACvC;YAAE,MAAM;YAAmB,MAAM;QAAO;QACxC;YAAE,MAAM;YAAgB,MAAM;QAAW;QACzC;YAAE,MAAM;YAAkB,MAAM;QAAW;KAC5C;IACD,QAAQ;QACN;YACE,MAAM;YACN,MAAM;YACN,MAAM,CAAC,sBACL,8OAAC;oBAAI,MAAK;oBAAe,SAAQ;oBAAa,GAAG,KAAK;8BACpD,cAAA,8OAAC;wBAAK,GAAE;;;;;;;;;;;QAGd;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,CAAC,sBACL,8OAAC;oBAAI,MAAK;oBAAe,SAAQ;oBAAa,GAAG,KAAK;8BACpD,cAAA,8OAAC;wBAAK,GAAE;;;;;;;;;;;QAGd;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,CAAC,sBACL,8OAAC;oBAAI,MAAK;oBAAe,SAAQ;oBAAa,GAAG,KAAK;8BACpD,cAAA,8OAAC;wBAAK,GAAE;;;;;;;;;;;QAGd;KACD;AACH;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,gIAAA,CAAA,UAAI;4CAAC,WAAU;;;;;;;;;;;kDAElB,8OAAC;wCAAE,WAAU;kDAAqC;;;;;;kDAKlD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iNAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;kEACrB,8OAAC;wDAAK,WAAU;kEAAgB;;;;;;;;;;;;0DAElC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,uNAAA,CAAA,eAAY;wDAAC,WAAU;;;;;;kEACxB,8OAAC;wDAAK,WAAU;kEAAgB;;;;;;;;;;;;0DAElC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,mNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;kEACtB,8OAAC;wDAAK,WAAU;kEAAgB;;;;;;;;;;;;;;;;;;;;;;;;0CAMtC,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,8OAAC;wCAAG,WAAU;kDACX,WAAW,QAAQ,CAAC,GAAG,CAAC,CAAC,qBACxB,8OAAC;0DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAU;;wDAET,KAAK,IAAI;sEACV,8OAAC,iPAAA,CAAA,4BAAyB;4DAAC,WAAU;;;;;;;;;;;;+CANhC,KAAK,IAAI;;;;;;;;;;;;;;;;0CAcxB,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,8OAAC;wCAAG,WAAU;kDACX,WAAW,OAAO,CAAC,GAAG,CAAC,CAAC,qBACvB,8OAAC;0DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAU;;wDAET,KAAK,IAAI;sEACV,8OAAC,iPAAA,CAAA,4BAAyB;4DAAC,WAAU;;;;;;;;;;;;+CANhC,KAAK,IAAI;;;;;;;;;;;;;;;;0CAcxB,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,8OAAC;wCAAG,WAAU;kDACX,WAAW,OAAO,CAAC,GAAG,CAAC,CAAC,qBACvB,8OAAC;0DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAU;;wDAET,KAAK,IAAI;sEACV,8OAAC,iPAAA,CAAA,4BAAyB;4DAAC,WAAU;;;;;;;;;;;;+CANhC,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAgB5B,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,8OAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;0CAE/B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,WAAU;;;;;;kDAEZ,8OAAC;wCAAO,WAAU;kDAA4D;;;;;;;;;;;;;;;;;;;;;;;8BAQpF,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;0CAAqC;;;;;;0CAKlD,8OAAC;gCAAI,WAAU;0CACZ,WAAW,MAAM,CAAC,GAAG,CAAC,CAAC,qBACtB,8OAAC,4JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAU;;0DAEV,8OAAC;gDAAK,WAAU;0DAAW,KAAK,IAAI;;;;;;0DACpC,8OAAC,KAAK,IAAI;gDAAC,WAAU;;;;;;;uCALhB,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAchC", "debugId": null}}, {"offset": {"line": 966, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/My%20Company%20Website/codnet-website/src/app/contact/page.tsx"], "sourcesContent": ["'use client';\n\nimport Header from '@/components/layout/Header';\nimport Footer from '@/components/layout/Footer';\nimport { motion } from 'framer-motion';\nimport { \n  PhoneIcon, \n  EnvelopeIcon, \n  MapPinIcon,\n  ClockIcon \n} from '@heroicons/react/24/outline';\n\nconst contactInfo = [\n  {\n    icon: PhoneIcon,\n    title: 'اتصل بنا',\n    details: ['+966 50 123 4567', '+966 11 234 5678'],\n    color: 'from-green-500 to-emerald-500'\n  },\n  {\n    icon: EnvelopeIcon,\n    title: 'راسلنا',\n    details: ['<EMAIL>', '<EMAIL>'],\n    color: 'from-blue-500 to-cyan-500'\n  },\n  {\n    icon: MapPinIcon,\n    title: 'زورنا',\n    details: ['الرياض، المملكة العربية السعودية', 'حي الملك فهد، طريق الملك عبدالعزيز'],\n    color: 'from-purple-500 to-pink-500'\n  },\n  {\n    icon: ClockIcon,\n    title: 'ساعات العمل',\n    details: ['الأحد - الخميس: 9:00 ص - 6:00 م', 'الجمعة - السبت: مغلق'],\n    color: 'from-amber-500 to-orange-500'\n  }\n];\n\nexport default function ContactPage() {\n  return (\n    <div className=\"page-container\">\n      <Header />\n      <main className=\"page-content pt-20\">\n        {/* Hero Section */}\n        <section className=\"section-container py-20 bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-slate-900 dark:via-blue-900 dark:to-indigo-900\">\n          <div className=\"content-wrapper text-center\">\n            <motion.div\n              initial={{ opacity: 0, y: 30 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6 }}\n            >\n              <h1 className=\"text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-6\">\n                تواصل معنا\n              </h1>\n              <p className=\"text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto\">\n                نحن هنا لمساعدتك في تحويل أفكارك إلى واقع رقمي. تواصل معنا اليوم لبدء رحلتك التقنية\n              </p>\n            </motion.div>\n          </div>\n        </section>\n\n        {/* Contact Info */}\n        <section className=\"section-container py-20 bg-white dark:bg-slate-900\">\n          <div className=\"content-wrapper\">\n            <div className=\"grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16\">\n              {contactInfo.map((info, index) => (\n                <motion.div\n                  key={info.title}\n                  initial={{ opacity: 0, y: 30 }}\n                  whileInView={{ opacity: 1, y: 0 }}\n                  viewport={{ once: true }}\n                  transition={{ duration: 0.6, delay: index * 0.1 }}\n                  className=\"text-center p-6 bg-gray-50 dark:bg-slate-800 rounded-2xl hover:shadow-lg transition-shadow duration-300\"\n                >\n                  <div className={`inline-flex p-4 rounded-2xl bg-gradient-to-br ${info.color} text-white mb-4`}>\n                    <info.icon className=\"h-8 w-8\" />\n                  </div>\n                  <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-3\">\n                    {info.title}\n                  </h3>\n                  {info.details.map((detail, detailIndex) => (\n                    <p key={detailIndex} className=\"text-gray-600 dark:text-gray-300 text-sm mb-1\">\n                      {detail}\n                    </p>\n                  ))}\n                </motion.div>\n              ))}\n            </div>\n\n            {/* Contact Form */}\n            <div className=\"grid lg:grid-cols-2 gap-12\">\n              <motion.div\n                initial={{ opacity: 0, x: -30 }}\n                whileInView={{ opacity: 1, x: 0 }}\n                viewport={{ once: true }}\n                transition={{ duration: 0.6 }}\n              >\n                <h2 className=\"text-3xl font-bold text-gray-900 dark:text-white mb-6\">\n                  أرسل لنا رسالة\n                </h2>\n                <form className=\"space-y-6\">\n                  <div className=\"grid md:grid-cols-2 gap-6\">\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                        الاسم الكامل\n                      </label>\n                      <input\n                        type=\"text\"\n                        className=\"form-input\"\n                        placeholder=\"أدخل اسمك الكامل\"\n                      />\n                    </div>\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                        البريد الإلكتروني\n                      </label>\n                      <input\n                        type=\"email\"\n                        className=\"form-input\"\n                        placeholder=\"أدخل بريدك الإلكتروني\"\n                      />\n                    </div>\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                      رقم الهاتف\n                    </label>\n                    <input\n                      type=\"tel\"\n                      className=\"form-input\"\n                      placeholder=\"أدخل رقم هاتفك\"\n                    />\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                      نوع المشروع\n                    </label>\n                    <select className=\"form-input\">\n                      <option>اختر نوع المشروع</option>\n                      <option>تطبيق هاتف</option>\n                      <option>موقع ويب</option>\n                      <option>تطبيق حاسوب</option>\n                      <option>استشارة تقنية</option>\n                    </select>\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                      تفاصيل المشروع\n                    </label>\n                    <textarea\n                      rows={6}\n                      className=\"form-input resize-vertical\"\n                      placeholder=\"اكتب تفاصيل مشروعك هنا...\"\n                    />\n                  </div>\n                  <button\n                    type=\"submit\"\n                    className=\"w-full btn-primary px-8 py-4 text-white font-bold rounded-xl text-lg\"\n                  >\n                    إرسال الرسالة\n                  </button>\n                </form>\n              </motion.div>\n\n              <motion.div\n                initial={{ opacity: 0, x: 30 }}\n                whileInView={{ opacity: 1, x: 0 }}\n                viewport={{ once: true }}\n                transition={{ duration: 0.6 }}\n                className=\"bg-gradient-to-br from-blue-600 to-purple-700 rounded-2xl p-8 text-white\"\n              >\n                <h3 className=\"text-2xl font-bold mb-6\">لماذا تختار Codnet؟</h3>\n                <div className=\"space-y-4\">\n                  {[\n                    'استشارة مجانية لمدة 30 دقيقة',\n                    'فريق خبراء متخصص',\n                    'ضمان الجودة والتسليم في الوقت المحدد',\n                    'دعم فني مستمر',\n                    'أسعار تنافسية',\n                    'تقنيات حديثة ومتطورة'\n                  ].map((benefit, index) => (\n                    <div key={index} className=\"flex items-center\">\n                      <div className=\"w-2 h-2 rounded-full bg-yellow-400 ml-3 flex-shrink-0\" />\n                      <span>{benefit}</span>\n                    </div>\n                  ))}\n                </div>\n                \n                <div className=\"mt-8 p-4 bg-white/10 rounded-xl\">\n                  <h4 className=\"font-semibold mb-2\">اتصل بنا الآن</h4>\n                  <p className=\"text-sm opacity-90\">\n                    للحصول على استشارة فورية أو لمناقشة مشروعك\n                  </p>\n                  <a href=\"tel:+966501234567\" className=\"inline-block mt-3 text-yellow-400 font-semibold\">\n                    +966 50 123 4567\n                  </a>\n                </div>\n              </motion.div>\n            </div>\n          </div>\n        </section>\n      </main>\n      <Footer />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AALA;;;;;;AAYA,MAAM,cAAc;IAClB;QACE,MAAM,iNAAA,CAAA,YAAS;QACf,OAAO;QACP,SAAS;YAAC;YAAoB;SAAmB;QACjD,OAAO;IACT;IACA;QACE,MAAM,uNAAA,CAAA,eAAY;QAClB,OAAO;QACP,SAAS;YAAC;YAAmB;SAAqB;QAClD,OAAO;IACT;IACA;QACE,MAAM,mNAAA,CAAA,aAAU;QAChB,OAAO;QACP,SAAS;YAAC;YAAoC;SAAqC;QACnF,OAAO;IACT;IACA;QACE,MAAM,iNAAA,CAAA,YAAS;QACf,OAAO;QACP,SAAS;YAAC;YAAmC;SAAuB;QACpE,OAAO;IACT;CACD;AAEc,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,sIAAA,CAAA,UAAM;;;;;0BACP,8OAAC;gBAAK,WAAU;;kCAEd,8OAAC;wBAAQ,WAAU;kCACjB,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;gCAAI;;kDAE5B,8OAAC;wCAAG,WAAU;kDAAoE;;;;;;kDAGlF,8OAAC;wCAAE,WAAU;kDAA6D;;;;;;;;;;;;;;;;;;;;;;kCAQhF,8OAAC;wBAAQ,WAAU;kCACjB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACZ,YAAY,GAAG,CAAC,CAAC,MAAM,sBACtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CAET,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,aAAa;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAChC,UAAU;gDAAE,MAAM;4CAAK;4CACvB,YAAY;gDAAE,UAAU;gDAAK,OAAO,QAAQ;4CAAI;4CAChD,WAAU;;8DAEV,8OAAC;oDAAI,WAAW,CAAC,8CAA8C,EAAE,KAAK,KAAK,CAAC,gBAAgB,CAAC;8DAC3F,cAAA,8OAAC,KAAK,IAAI;wDAAC,WAAU;;;;;;;;;;;8DAEvB,8OAAC;oDAAG,WAAU;8DACX,KAAK,KAAK;;;;;;gDAEZ,KAAK,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,4BACzB,8OAAC;wDAAoB,WAAU;kEAC5B;uDADK;;;;;;2CAdL,KAAK,KAAK;;;;;;;;;;8CAuBrB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,SAAS;gDAAE,SAAS;gDAAG,GAAG,CAAC;4CAAG;4CAC9B,aAAa;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAChC,UAAU;gDAAE,MAAM;4CAAK;4CACvB,YAAY;gDAAE,UAAU;4CAAI;;8DAE5B,8OAAC;oDAAG,WAAU;8DAAwD;;;;;;8DAGtE,8OAAC;oDAAK,WAAU;;sEACd,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;;sFACC,8OAAC;4EAAM,WAAU;sFAAkE;;;;;;sFAGnF,8OAAC;4EACC,MAAK;4EACL,WAAU;4EACV,aAAY;;;;;;;;;;;;8EAGhB,8OAAC;;sFACC,8OAAC;4EAAM,WAAU;sFAAkE;;;;;;sFAGnF,8OAAC;4EACC,MAAK;4EACL,WAAU;4EACV,aAAY;;;;;;;;;;;;;;;;;;sEAIlB,8OAAC;;8EACC,8OAAC;oEAAM,WAAU;8EAAkE;;;;;;8EAGnF,8OAAC;oEACC,MAAK;oEACL,WAAU;oEACV,aAAY;;;;;;;;;;;;sEAGhB,8OAAC;;8EACC,8OAAC;oEAAM,WAAU;8EAAkE;;;;;;8EAGnF,8OAAC;oEAAO,WAAU;;sFAChB,8OAAC;sFAAO;;;;;;sFACR,8OAAC;sFAAO;;;;;;sFACR,8OAAC;sFAAO;;;;;;sFACR,8OAAC;sFAAO;;;;;;sFACR,8OAAC;sFAAO;;;;;;;;;;;;;;;;;;sEAGZ,8OAAC;;8EACC,8OAAC;oEAAM,WAAU;8EAAkE;;;;;;8EAGnF,8OAAC;oEACC,MAAM;oEACN,WAAU;oEACV,aAAY;;;;;;;;;;;;sEAGhB,8OAAC;4DACC,MAAK;4DACL,WAAU;sEACX;;;;;;;;;;;;;;;;;;sDAML,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,aAAa;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAChC,UAAU;gDAAE,MAAM;4CAAK;4CACvB,YAAY;gDAAE,UAAU;4CAAI;4CAC5B,WAAU;;8DAEV,8OAAC;oDAAG,WAAU;8DAA0B;;;;;;8DACxC,8OAAC;oDAAI,WAAU;8DACZ;wDACC;wDACA;wDACA;wDACA;wDACA;wDACA;qDACD,CAAC,GAAG,CAAC,CAAC,SAAS,sBACd,8OAAC;4DAAgB,WAAU;;8EACzB,8OAAC;oEAAI,WAAU;;;;;;8EACf,8OAAC;8EAAM;;;;;;;2DAFC;;;;;;;;;;8DAOd,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEAAqB;;;;;;sEACnC,8OAAC;4DAAE,WAAU;sEAAqB;;;;;;sEAGlC,8OAAC;4DAAE,MAAK;4DAAoB,WAAU;sEAAkD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASpG,8OAAC,sIAAA,CAAA,UAAM;;;;;;;;;;;AAGb", "debugId": null}}]}