'use client';

import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import { motion } from 'framer-motion';
import { 
  LightBulbIcon,
  ShieldCheckIcon,
  UserGroupIcon,
  TrophyIcon,
  RocketLaunchIcon,
  HeartIcon
} from '@heroicons/react/24/outline';

const values = [
  {
    icon: LightBulbIcon,
    title: 'الابتكار',
    description: 'نسعى دائماً لتقديم حلول مبتكرة ومتطورة تواكب أحدث التقنيات',
    color: 'from-yellow-500 to-orange-500'
  },
  {
    icon: ShieldCheckIcon,
    title: 'الجودة',
    description: 'نلتزم بأعلى معايير الجودة في كل مرحلة من مراحل التطوير',
    color: 'from-green-500 to-emerald-500'
  },
  {
    icon: UserGroupIcon,
    title: 'العمل الجماعي',
    description: 'نؤمن بقوة العمل الجماعي والتعاون لتحقيق أفضل النتائج',
    color: 'from-blue-500 to-cyan-500'
  },
  {
    icon: TrophyIcon,
    title: 'التميز',
    description: 'نسعى للتميز في كل ما نقوم به ونهدف لتجاوز توقعات عملائنا',
    color: 'from-purple-500 to-pink-500'
  },
  {
    icon: RocketLaunchIcon,
    title: 'النمو المستمر',
    description: 'نؤمن بالتطوير المستمر لمهاراتنا ومواكبة التطورات التقنية',
    color: 'from-indigo-500 to-purple-500'
  },
  {
    icon: HeartIcon,
    title: 'الشغف',
    description: 'نحب ما نعمل ونضع شغفنا في كل مشروع نقوم بتطويره',
    color: 'from-red-500 to-pink-500'
  }
];

const stats = [
  { number: '100+', label: 'مشروع مكتمل', icon: '🚀' },
  { number: '50+', label: 'عميل راضي', icon: '😊' },
  { number: '5+', label: 'سنوات خبرة', icon: '⭐' },
  { number: '15+', label: 'خبير تقني', icon: '👨‍💻' }
];

const timeline = [
  {
    year: '2019',
    title: 'البداية',
    description: 'تأسيس Codnet برؤية واضحة لتقديم حلول تقنية مبتكرة'
  },
  {
    year: '2020',
    title: 'النمو',
    description: 'توسيع الفريق وتطوير أول 20 مشروع ناجح'
  },
  {
    year: '2021',
    title: 'التطوير',
    description: 'إطلاق خدمات جديدة وتحقيق شراكات استراتيجية'
  },
  {
    year: '2022',
    title: 'التوسع',
    description: 'فتح مكاتب جديدة وزيادة حجم الفريق إلى 15 خبير'
  },
  {
    year: '2023',
    title: 'الريادة',
    description: 'تحقيق مكانة رائدة في السوق مع أكثر من 50 عميل راضي'
  },
  {
    year: '2024',
    title: 'المستقبل',
    description: 'خطط طموحة للتوسع والابتكار في التقنيات الناشئة'
  }
];

export default function AboutPage() {
  return (
    <div className="page-container">
      <Header />
      <main className="page-content pt-20">
        {/* Hero Section */}
        <section className="section-container py-20 bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-slate-900 dark:via-blue-900 dark:to-indigo-900">
          <div className="content-wrapper">
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              <motion.div
                initial={{ opacity: 0, x: -30 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6 }}
              >
                <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-6">
                  من نحن
                </h1>
                <p className="text-xl text-gray-600 dark:text-gray-300 mb-8 leading-relaxed">
                  نحن فريق من المطورين والمصممين المتخصصين في تطوير الحلول التقنية المبتكرة. 
                  نؤمن بقوة التكنولوجيا في تحويل الأفكار إلى واقع رقمي يدفع نمو الأعمال.
                </p>
                <div className="flex flex-col sm:flex-row gap-4">
                  <a
                    href="/contact"
                    className="btn-primary inline-flex items-center px-8 py-4 text-white font-bold rounded-xl text-lg"
                  >
                    تواصل معنا
                  </a>
                  <a
                    href="/portfolio"
                    className="inline-flex items-center px-8 py-4 border-2 border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white dark:border-blue-400 dark:text-blue-400 dark:hover:bg-blue-400 dark:hover:text-gray-900 font-semibold rounded-xl transition-all duration-300"
                  >
                    شاهد أعمالنا
                  </a>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: 30 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="relative"
              >
                <div className="bg-gradient-to-br from-blue-600 to-purple-700 rounded-2xl p-8 text-white">
                  <h3 className="text-2xl font-bold mb-4">رؤيتنا</h3>
                  <p className="mb-6">
                    أن نكون الشريك التقني الأول للشركات في رحلتها نحو التحول الرقمي
                  </p>
                  <h3 className="text-2xl font-bold mb-4">مهمتنا</h3>
                  <p>
                    تطوير حلول تقنية مبتكرة وعالية الجودة تساعد عملاءنا على تحقيق أهدافهم وتطوير أعمالهم
                  </p>
                </div>
              </motion.div>
            </div>
          </div>
        </section>

        {/* Stats Section */}
        <section className="section-container py-20 bg-white dark:bg-slate-900">
          <div className="content-wrapper">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
              {stats.map((stat, index) => (
                <motion.div
                  key={stat.label}
                  initial={{ opacity: 0, scale: 0.8 }}
                  whileInView={{ opacity: 1, scale: 1 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="text-center p-6 bg-gray-50 dark:bg-slate-800 rounded-2xl"
                >
                  <div className="text-4xl mb-2">{stat.icon}</div>
                  <div className="text-2xl md:text-3xl font-bold text-blue-600 dark:text-blue-400 mb-1">
                    {stat.number}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    {stat.label}
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Values Section */}
        <section className="section-container py-20 bg-gray-50 dark:bg-slate-800">
          <div className="content-wrapper">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
              className="text-center mb-16"
            >
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
                قيمنا الأساسية
              </h2>
              <p className="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                القيم التي نؤمن بها وتوجه عملنا اليومي في تطوير الحلول التقنية
              </p>
            </motion.div>

            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {values.map((value, index) => (
                <motion.div
                  key={value.title}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="bg-white dark:bg-slate-900 p-6 rounded-2xl shadow-lg hover:shadow-xl transition-shadow duration-300"
                >
                  <div className={`inline-flex p-3 rounded-xl bg-gradient-to-br ${value.color} text-white mb-4`}>
                    <value.icon className="h-6 w-6" />
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-3">
                    {value.title}
                  </h3>
                  <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                    {value.description}
                  </p>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Timeline Section */}
        <section className="section-container py-20 bg-white dark:bg-slate-900">
          <div className="content-wrapper">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
              className="text-center mb-16"
            >
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
                رحلتنا عبر السنوات
              </h2>
              <p className="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                تعرف على مسيرة Codnet من البداية حتى اليوم
              </p>
            </motion.div>

            <div className="relative">
              {/* Timeline line */}
              <div className="absolute right-1/2 transform translate-x-1/2 w-1 h-full bg-blue-200 dark:bg-blue-800"></div>
              
              <div className="space-y-12">
                {timeline.map((item, index) => (
                  <motion.div
                    key={item.year}
                    initial={{ opacity: 0, x: index % 2 === 0 ? -30 : 30 }}
                    whileInView={{ opacity: 1, x: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                    className={`relative flex items-center ${
                      index % 2 === 0 ? 'flex-row-reverse' : ''
                    }`}
                  >
                    {/* Timeline dot */}
                    <div className="absolute right-1/2 transform translate-x-1/2 w-4 h-4 bg-blue-600 rounded-full border-4 border-white dark:border-slate-900"></div>
                    
                    <div className={`w-1/2 ${index % 2 === 0 ? 'pl-8' : 'pr-8'}`}>
                      <div className="bg-gray-50 dark:bg-slate-800 p-6 rounded-2xl">
                        <div className="text-2xl font-bold text-blue-600 dark:text-blue-400 mb-2">
                          {item.year}
                        </div>
                        <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
                          {item.title}
                        </h3>
                        <p className="text-gray-600 dark:text-gray-300">
                          {item.description}
                        </p>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </div>
  );
}
