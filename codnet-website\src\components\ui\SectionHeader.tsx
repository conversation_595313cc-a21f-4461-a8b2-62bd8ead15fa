import { ReactNode } from 'react';
import { motion } from 'framer-motion';

interface SectionHeaderProps {
  title: string;
  subtitle?: string;
  description?: string;
  badge?: string;
  children?: ReactNode;
  align?: 'left' | 'center' | 'right';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  animate?: boolean;
}

export default function SectionHeader({
  title,
  subtitle,
  description,
  badge,
  children,
  align = 'center',
  size = 'md',
  className = '',
  animate = true
}: SectionHeaderProps) {
  const alignClasses = {
    left: 'text-right',
    center: 'text-center',
    right: 'text-left'
  };

  const titleSizeClasses = {
    sm: 'text-2xl md:text-3xl',
    md: 'text-3xl md:text-4xl lg:text-5xl',
    lg: 'text-4xl md:text-5xl lg:text-6xl'
  };

  const subtitleSizeClasses = {
    sm: 'text-lg',
    md: 'text-lg md:text-xl',
    lg: 'text-xl md:text-2xl'
  };

  const containerClasses = `
    ${alignClasses[align]}
    mb-16
    ${className}
  `.trim().replace(/\s+/g, ' ');

  const content = (
    <div className={containerClasses}>
      {badge && (
        <motion.div
          initial={animate ? { opacity: 0, y: 20 } : {}}
          whileInView={animate ? { opacity: 1, y: 0 } : {}}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="inline-flex items-center px-4 py-2 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200 text-sm font-medium mb-6"
        >
          <span className="w-2 h-2 bg-blue-500 rounded-full ml-2 animate-pulse"></span>
          {badge}
        </motion.div>
      )}

      {subtitle && (
        <motion.p
          initial={animate ? { opacity: 0, y: 20 } : {}}
          whileInView={animate ? { opacity: 1, y: 0 } : {}}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.1 }}
          className={`${subtitleSizeClasses[size]} text-blue-600 dark:text-blue-400 font-semibold mb-4`}
        >
          {subtitle}
        </motion.p>
      )}

      <motion.h2
        initial={animate ? { opacity: 0, y: 30 } : {}}
        whileInView={animate ? { opacity: 1, y: 0 } : {}}
        viewport={{ once: true }}
        transition={{ duration: 0.6, delay: 0.2 }}
        className={`${titleSizeClasses[size]} font-bold text-gray-900 dark:text-white mb-4 leading-tight`}
      >
        {title}
      </motion.h2>

      {description && (
        <motion.p
          initial={animate ? { opacity: 0, y: 30 } : {}}
          whileInView={animate ? { opacity: 1, y: 0 } : {}}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.3 }}
          className="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto leading-relaxed"
        >
          {description}
        </motion.p>
      )}

      {children && (
        <motion.div
          initial={animate ? { opacity: 0, y: 30 } : {}}
          whileInView={animate ? { opacity: 1, y: 0 } : {}}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="mt-8"
        >
          {children}
        </motion.div>
      )}
    </div>
  );

  return content;
}
